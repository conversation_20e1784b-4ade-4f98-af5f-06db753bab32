[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "fuzzlm-agent"
version = "1.0.0-dev"
description = "基于LLM智能体的C/C++专门化模糊测试编排框架，集成LibAFL"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "FuzzLM-Agent Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "FuzzLM-Agent Team", email = "<EMAIL>"}
]
keywords = ["fuzzing", "llm", "ai", "security", "testing", "libafl", "cpp", "memory-safety"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research", 
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10", 
    "Programming Language :: Python :: 3.11",
    "Topic :: Security",
    "Topic :: Software Development :: Testing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: C",
    "Programming Language :: C++"
]
requires-python = ">=3.9"
dependencies = [
    # 核心框架依赖
    "pydantic>=2.0",
    "typer>=0.9.0",
    "click>=8.0",
    "pyyaml>=6.0",

    # LLM客户端依赖
    "anthropic>=0.25.0",
    "openai>=1.0.0",
    "litellm>=1.35.0",

    # RAG和向量数据库依赖  
    # "sentence-transformers>=2.2.0",  # 现使用远程Ollama embedding服务
    "faiss-cpu>=1.7.0",

    # 通信和IPC依赖
    "grpcio>=1.59.0",
    "grpcio-tools>=1.59.0",

    # 代码分析依赖
    "tree-sitter>=0.20.0",

    # 数据处理依赖
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "json-repair>=0.25.0",  # LLM响应JSON修复库

    # 系统监控依赖
    "psutil>=5.9.0",

    # CLI界面依赖
    "rich>=13.0.0",

    # 数据库依赖
    "aiosqlite>=0.19.0",

    # 异步文件操作依赖
    "aiofiles>=23.0.0",

    # 模板引擎依赖
    "jinja2>=3.0.0"
]

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "mypy>=1.5.0", 
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pre-commit>=3.3.0",
    "ruff>=0.0.280"
]
docs = [
    "sphinx>=7.0.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0"
]


[project.scripts]
fuzzlm-agent = "fuzzlm_agent.interfaces.cli:main"

[project.urls]
Homepage = "https://github.com/your-org/fuzzlm-agent"
Documentation = "https://fuzzlm-agent.readthedocs.io"
Repository = "https://github.com/your-org/fuzzlm-agent.git"
"Bug Tracker" = "https://github.com/your-org/fuzzlm-agent/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["fuzzlm_agent*"]
exclude = ["docs*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''
exclude = '''
.*_pb2(_grpc)?\.py
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "fuzzlm_agent/infrastructure/grpc/.*_pb2\\.py$",
    "fuzzlm_agent/infrastructure/grpc/.*_pb2_grpc\\.py$",
    "fuzzlm_agent/infrastructure/shared_memory/.*_pb2\\.py$"
]

[[tool.mypy.overrides]]
module = [
    "anthropic.*",
    "openai.*",
    "sentence_transformers.*",
    "faiss.*",
    "tree_sitter.*",
    "matplotlib.*",
    "seaborn.*",
    "pandas.*",
    "sklearn.*",
    "jinja2.*",
    "tabulate.*",
    "weasyprint.*",
    "plotly.*",
    "dash.*",
    "networkx.*",
    "statsmodels.*",
    "grpc.*",
    "grpcio.*",
    "json_repair.*"
]
ignore_missing_imports = true

# 忽略所有 protobuf 生成的文件
[[tool.mypy.overrides]]
module = [
    "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2",
    "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2_grpc",
    "fuzzlm_agent.infrastructure.grpc.llm_generation_pb2",
    "fuzzlm_agent.infrastructure.grpc.llm_generation_pb2_grpc",
    "fuzzlm_agent.infrastructure.shared_memory.telemetry_pb2"
]
ignore_errors = true

[tool.coverage.run]
source = ["fuzzlm_agent"]
omit = [
    "*/__main__.py",
    "*_pb2.py",
    "*_pb2_grpc.py"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]

[tool.ruff]
target-version = "py39"
line-length = 88
extend-exclude = ["*_pb2.py", "*_pb2_grpc.py"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"] 