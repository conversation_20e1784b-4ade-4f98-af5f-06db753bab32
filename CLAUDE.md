# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

FuzzLM-Agent is an intelligent fuzzing orchestration framework that leverages Large Language Models (LLMs) to dynamically compose fuzzing strategies using LibAFL. The system consists of:

- **Python Orchestrator** (`fuzzlm_agent/`): Core LLM-driven strategy formulation and execution management
- **Rust Fuzzing Engine** (`fuzzlm_agent/fuzzing-engine/`): LibAFL-based fuzzing execution runtime
- **LibAFL Integration** (`LibAFL/`, `libafl_fuzzbench/`): Fuzzing framework and benchmark implementations

## Core Development Principles

To ensure development aligns with the project's research goals, the following principles must be strictly followed:

1. Source of Truth
    
    The project is developed based on the research paper (`docs/research_paper.md`) and the workflow specification (`docs/workflow.md`). In case of any ambiguity or contradiction, these two documents are the definitive source of truth and must be prioritized over any conflicting implementation.
    
2. Design Change Control
    
    As a rule, the source of truth documents should not be modified. If a flaw or a necessary improvement in the original design is discovered during development, the following protocol is mandatory:
    
    - **<PERSON><PERSON> and <PERSON>uss:** Pause development on the affected components and consult with the user to reach a consensus on the solution.
    - **Update Before Continuing:** After a resolution is agreed upon, the relevant source of truth document(s) must be updated _before_ resuming development.

3. Documentation for Research
    
    The project's final output directly supports a research paper. Document key design choices, architectural decisions, and their underlying rationale as they occur. This ongoing record-keeping is essential for the final paper-writing phase.

## Architecture

The system follows a **simplified dual-core architecture** (完全符合workflow.md设计):

### Orchestrator Layer (`fuzzlm_agent/orchestrator/`) - **架构核心**
- **main_orchestrator.py**: 主编排器，统一的Campaign生命周期管理
- **phase_processors.py**: 6个工作流阶段处理器 (Phase0-Phase5)
  - Phase 0: 系统初始化和环境准备
  - Phase 1: "热启动"初始策略生成  
  - Phase 2: 代码生成与复合验证
  - Phase 3: 生产运行和自适应状态感知
  - Phase 4: 策略优化与"影子"验证
  - Phase 5: 反思学习与知识演化

### Core Layer (`fuzzlm_agent/core/`) - **核心组件**
- **reflection_engine.py**: 核心反思引擎实现
- **statistical_analysis.py**: 统计分析工具
- **system_integration_validator.py**: 系统集成验证器

### Domain Layer (`fuzzlm_agent/domain/`)
- **schemas.py**: 共享数据结构和业务模型
- **evaluation_models.py**: 性能评估模型

### Infrastructure Layer (`fuzzlm_agent/infrastructure/`) - **简化通信架构**
- **grpc/client.py**: gRPC控制平面客户端
- **shared_memory/telemetry_stream.py**: 共享内存数据平面
- **llm/client.py**: LLM提供商抽象和管理
- **compilation/error_handler.py**: Rust编译和错误处理

### Knowledge Management (`fuzzlm_agent/knowledge/`) - **RAG系统**
- **unified_experience_manager.py**: 经验管理器，集成RAG和向量存储
- **unified_vector_store.py**: 向量存储 (FAISS + 元数据管理)
- **migration_helper.py**: 知识库迁移和兼容性工具

### Other Components
- **prompts/**: LLM prompt模板和管理
- **interfaces/cli.py**: 命令行接口

## Development Commands

### Environment Setup
```bash
# Install Python dependencies
pip install -r requirements.txt
# or
pip install -e .
```

### 构建Rust模糊测试引擎
```bash
# 推荐: 使用构建脚本
cd scripts
./build_rust_runtime.sh

# 或手动构建:
cd fuzzlm_agent/fuzzing-engine
cargo build --release

# 编译后的二进制文件将位于:
# fuzzlm_agent/fuzzing-engine/target/release/fuzzing-engine

# 运行模糊测试引擎
./target/release/fuzzing-engine --grpc-address 0.0.0.0:50051
```

### Protobuf 代码生成
```bash
# 生成/更新 gRPC 客户端代码（当 .proto 文件变更时）
./scripts/generate_grpc_proto.sh

# 注意：遥测数据现在使用 protobuf 格式进行跨语言兼容
# protobuf 定义位于 fuzzlm_agent/infrastructure/shared_memory/telemetry.proto

# 手动生成 Python gRPC 代码
cd fuzzlm_agent/infrastructure/grpc
python -m grpc_tools.protoc --python_out=. --grpc_python_out=. --proto_path=. fuzzing_control.proto
python -m grpc_tools.protoc --python_out=. --grpc_python_out=. --proto_path=. llm_generation.proto
```

### 通信接口测试
```bash
# 测试 gRPC 和共享内存通信（推荐）
./scripts/test_communication.sh

# 直接运行 Python 测试脚本
python ./scripts/test_communication.py

# 指定自定义 gRPC 地址
python ./scripts/test_communication.py --grpc-address 127.0.0.1:8080

# 详细输出模式
python ./scripts/test_communication.py --verbose
```

### 代码质量检查
```bash
# 格式化 Python 代码
black fuzzlm_agent/

# 类型检查
mypy fuzzlm_agent/

# 代码风格检查
ruff check fuzzlm_agent/

# 自动修复可自动修复的代码风格问题
ruff check fuzzlm_agent/ --fix

# Run all quality checks
black fuzzlm_agent/ && mypy fuzzlm_agent/ && ruff check fuzzlm_agent/
```

### 运行系统

#### 使用系统

**CLI接口（推荐）:**
```bash
# 运行基本Campaign
python -m fuzzlm_agent start target_program.c

# 指定运行时长
python -m fuzzlm_agent start target_program.c --duration 2.0

# 使用自定义配置
python -m fuzzlm_agent start target_program.c --config config.yaml

# 查看帮助
python -m fuzzlm_agent --help
```

### Configuration
Copy `config.example.yaml` to `config.yaml` and configure:
- LLM provider (OpenAI/Anthropic/OpenRouter)
- API keys and endpoints
- Fuzzing parameters
- Knowledge base settings

## 通信协议详情

### gRPC 服务接口
基于 `fuzzing_control_pb2_grpc.FuzzingControlStub` 提供以下服务：

- `StartFuzzer`: 启动 Champion 或 Shadow Fuzzer 实例
- `StopFuzzer`: 停止指定 Fuzzer 实例
- `UpdateStrategy`: 动态更新模糊测试策略
- `SpawnShadow`: 从 Champion 创建 Shadow 进程
- `PromoteShadow`: 将 Shadow 提升为新的 Champion
- `ValidateCode`: 复合验证沙箱代码验证
- `HealthCheck`: 系统健康检查和指标获取

### LLM-Driven Mutator Generation (新功能)
Rust fuzzing engine 现已支持 LLM 驱动的变异器生成：

- **智能生成**: LLM 分析目标程序特征，生成定制化的 fuzzing mutators
- **复合验证**: 四阶段验证（静态分析、编译、动态行为、效用探测）
- **重试机制**: 最多 3 次重试，每次迭代都基于错误反馈改进
- **无缝集成**: 生成的 mutators 直接集成到 LibAFL fuzzing 循环中

详细文档：
- [快速入门指南](fuzzlm_agent/fuzzing-engine/docs/quick_start_llm_mutators.md)
- [功能文档](fuzzlm_agent/fuzzing-engine/docs/llm_driven_mutator_generation.md)
- [API 参考](fuzzlm_agent/fuzzing-engine/docs/api_reference.md)

#### 实现策略说明（重要）

**原始设计 vs 当前实现**：
虽然项目定义了完整的 `llm_generation.proto` gRPC 服务接口，但当前实现采用了简化的集成策略：

- **原始设计**: 独立的 LLM 代码生成 gRPC 服务，支持 Orchestrator 与 Runtime 之间的分离式通信
- **当前实现**: 直接在 Phase 1 策略生成阶段调用 LLM，通过 `RuntimeClient.custom_code` 字段传递生成的代码
- **设计决策**: 作为研究原型，优先验证核心概念而非完整的生产架构
- **影响**: 功能完整实现，但架构更加紧凑，减少了服务间通信开销

**相关文件**：
- `fuzzlm_agent/orchestrator/phase1_strategy.py` - 实际的 LLM 代码生成集成点
- `fuzzlm_agent/infrastructure/grpc/llm_generation.proto` - 保留的实验性接口定义（标注为未来功能）
- `fuzzlm_agent/fuzzing-engine/src/strategy_builder.rs` - Rust 端代码编译和验证实现

**注意**: 如果您看到 `llm_generation_pb2.py` 等 gRPC 生成代码，这些是自动生成的存根，当前并未实际使用。

### 共享内存遥测数据格式
遵循 protobuf 协议，包含：

- **TelemetryEntry**: 统一的遥测条目消息格式
- **数据类型**: ExecutionStats, CoverageHit, CrashFound, CorpusGrow, MutatorStats 等
- **版本控制**: 版本2表示protobuf格式，提供向后兼容性
- **跨语言兼容**: Python和Rust使用相同的protobuf定义确保数据一致性

## 重要注意事项

- 系统需要Python 3.9+和Rust工具链
- LibAFL子模块必须正确初始化和构建
- 配置文件包含API密钥（保持安全）
- 系统默认使用真实模糊测试模式 (`system.simulation_mode: false`)
- gRPC 通信需要 `grpcio>=1.59.0` 和 `grpcio-tools>=1.59.0`

### 路径配置

系统使用智能路径解析，具有以下优先级：

1. **Environment Variables** (最高优先级):
   - `FUZZLM_PROJECT_ROOT`: 项目根目录
   - `FUZZLM_LIBAFL_PATH`: LibAFL库路径
   - `FUZZLM_ENGINE_PATH`: 模糊测试引擎二进制路径
   - `FUZZLM_FUZZBENCH_PATH`: libafl_fuzzbench目录路径

2. **自动检测**: 系统将搜索相对于项目根目录的路径
3. **配置文件**: 配置文件中指定的路径 (最低优先级)

## File Structure Notes

- **Documentation** is organized with clear hierarchy:
  - `QUICK_START.md` - **5分钟快速上手指南** (推荐新用户)
  - `CLAUDE.md` - 与CLAUDE CODE协作开发的文档（本文件）
  - `docs/research_paper.md` - Research paper (source of truth)
  - `docs/workflow.md` - Workflow specification (source of truth)
  - `fuzzlm_agent/infrastructure/shared_memory/telemetry.proto` - 遥测数据protobuf定义
- **Scripts** are minimal with only essential build tools:
  - `scripts/build_rust_runtime.sh` - Core Rust runtime build script
  - `scripts/generate_grpc_proto.sh` - gRPC和遥测数据protobuf代码生成
- **Configuration** is unified:
  - `config.example.yaml` - Complete configuration template
  - `config.yaml` - User configuration file
- Knowledge base files are stored in `data/`
- LibAFL is included as a large submodule with its own build system

### LLM Client Usage Guidelines
The project uses a LiteLLM-based client architecture to support 100+ LLM providers:

**Production Code:**
```python
from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient

# 使用配置字典初始化
config = {
    "model": "openrouter/qwen/qwen3-coder:free",
    "api_key": "your-key",  # 或从环境变量 OPENROUTER_API_KEY 读取
    "temperature": 0.1,
    "max_retries": 3
}
client = LiteLLMClient(config)

# 使用客户端
response = client.chat(messages=[{"role": "user", "content": "Hello"}])
```

**注意**: 
- 项目使用 `LiteLLMClient` 而非 `create_llm_client()` 函数
- 配置通过字典传递，而非 `UnifiedLLMConfig` 类
- 支持100+提供商，包括 OpenRouter、OpenAI、Anthropic 等

### JSON Parsing Guidelines
For LLM response parsing, the project directly uses the `json_repair` library for robust JSON extraction:

**LLM Response Parsing:**
```python
import json_repair

# 直接从LLM响应中解析JSON
data = json_repair.loads(llm_response)

# json_repair 可以处理格式不规范的JSON
# 无需额外的封装或错误处理类
```

**重要规则:**
- ✅ **LLM响应解析**: 直接使用 `json_repair.loads()` 
- ✅ **通用JSON**: 使用标准 `json` 模块
- ✅ **格式修复**: `json_repair` 自动处理常见的JSON格式问题

### Protobuf 遥测系统指南

项目使用 protobuf 进行遥测数据传输，确保跨语言兼容性和类型安全：

**Protobuf 代码生成:**
```bash
# 生成/更新所有 protobuf 代码（包含遥测数据）
./scripts/generate_grpc_proto.sh

# 手动生成遥测 protobuf（如需要）
cd fuzzlm_agent/infrastructure/shared_memory
protoc --python_out=. --proto_path=. telemetry.proto
```

**遥测数据格式规范:**
- protobuf 定义: `fuzzlm_agent/infrastructure/shared_memory/telemetry.proto`
- 版本标识: 版本2表示protobuf格式
- 消息类型: TelemetryEntry 作为统一容器
- 数据类型: ExecutionStats, CoverageHit, CrashFound, CorpusGrow 等

**重要规则:**
- ✅ **Protobuf优先**: 遥测数据使用 protobuf 确保类型安全
- ✅ **自动生成**: 使用 `generate_grpc_proto.sh` 脚本维护代码同步
- ✅ **跨语言兼容**: Python和Rust使用相同protobuf定义确保一致性

### Async Processing Guidelines
The project uses standard Python asyncio patterns for asynchronous operations:

**Async Processing:**
```python
import asyncio
import logging

async def safe_await(coro, operation_name, timeout=30.0, logger=None):
    """安全的异步操作包装器"""
    try:
        return await asyncio.wait_for(coro, timeout=timeout)
    except Exception as e:
        if logger:
            logger.error(f"{operation_name} 失败: {e}")
        raise

# 示例使用
result = await safe_await(
    some_async_operation(),
    "operation_name",
    timeout=30.0,
    logger=logging.getLogger(__name__)
)
```

**重要规则:**
- ✅ **使用标准库**: asyncio、logging
- ✅ **错误处理**: 使用标准的try/except和日志记录
- ✅ **超时控制**: 使用asyncio.wait_for()进行超时控制