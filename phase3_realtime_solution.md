# Phase 3 实时响应机制改进方案

## 问题总结

当前 Phase 3 实现存在以下关键问题：

1. **阻塞式执行**：`analyze_telemetry_stream` 会阻塞运行整个 fuzzing duration
2. **延迟的 LLM 分析**：LLM 状态感知分析在整个过程结束后才执行
3. **违背设计规范**：未实现"每10分钟向 LLM 发送数据"的实时响应要求

## 解决方案

### 方案 1：重构为并发任务架构（推荐）

```python
async def phase3_production_run(
    ctx: CampaignContext,
    runtime_client: RuntimeClient,
    llm_client: LiteLLMClient,
    telemetry_reader: TelemetryReader | None,
    duration_hours: float,
) -> Any:
    """改进的 Phase 3 实现，支持真正的实时响应"""
    
    # 启动 champion fuzzer
    fuzzer_id = await runtime_client.start_fuzzer(
        fuzzer_type="champion",
        strategy=ctx.strategy,
        target_path=ctx.target_path,
    )
    
    # 状态追踪
    state_analyses = []
    needs_adjustment = False
    telemetry_analyzer = TelemetryAnalyzer()
    
    # 创建并发任务
    async def telemetry_collection_task():
        """持续收集遥测数据的任务"""
        nonlocal telemetry_analyzer
        start_time = datetime.now(timezone.utc)
        end_time = start_time + timedelta(hours=duration_hours)
        
        while datetime.now(timezone.utc) < end_time:
            entries = await telemetry_reader.read_batch(max_entries=1000)
            for entry in entries:
                instance_id = entry.get("instance_id", "default")
                if instance_id not in telemetry_analyzer.aggregators:
                    telemetry_analyzer.aggregators[instance_id] = TelemetryAggregator()
                telemetry_analyzer.aggregators[instance_id].process_telemetry_entry(entry)
            await asyncio.sleep(0.1)
    
    async def llm_analysis_task():
        """定期进行 LLM 状态分析的任务"""
        nonlocal needs_adjustment, state_analyses
        analysis_interval = timedelta(minutes=10)
        last_analysis = datetime.now(timezone.utc)
        
        while True:
            current_time = datetime.now(timezone.utc)
            if current_time - last_analysis >= analysis_interval:
                # 获取最近的性能指标
                metrics_list = []
                for aggregator in telemetry_analyzer.aggregators.values():
                    metrics = aggregator.get_current_metrics()
                    metrics_list.append(metrics)
                
                if metrics_list:
                    # 发送给 LLM 进行实时分析
                    logger.info("Sending real-time metrics to LLM for state analysis")
                    for metrics in metrics_list:
                        analysis = await _analyze_state_with_metrics(
                            llm_client, metrics, ctx
                        )
                        state_analyses.append(analysis)
                        
                        if analysis.needs_adjustment:
                            logger.warning(
                                f"LLM detected need for adjustment: {analysis.bottlenecks}"
                            )
                            needs_adjustment = True
                            # 可以在这里触发 Phase 4 的影子验证
                
                last_analysis = current_time
            
            await asyncio.sleep(30)  # 每30秒检查一次
    
    async def anomaly_detection_task():
        """实时异常检测任务"""
        window_interval = timedelta(seconds=60)
        last_check = datetime.now(timezone.utc)
        
        while True:
            current_time = datetime.now(timezone.utc)
            if current_time - last_check >= window_interval:
                for aggregator in telemetry_analyzer.aggregators.values():
                    metrics = aggregator.get_current_metrics()
                    anomalies = telemetry_analyzer.detect_anomalies(metrics)
                    
                    if anomalies:
                        logger.warning(f"Anomalies detected: {anomalies}")
                        # 立即触发 LLM 分析
                        analysis = await _analyze_state_with_metrics(
                            llm_client, metrics, ctx, anomalies=anomalies
                        )
                        state_analyses.append(analysis)
                        if analysis.needs_adjustment:
                            needs_adjustment = True
                
                last_check = current_time
            
            await asyncio.sleep(10)
    
    try:
        # 并发运行所有任务
        tasks = [
            asyncio.create_task(telemetry_collection_task()),
            asyncio.create_task(llm_analysis_task()),
            asyncio.create_task(anomaly_detection_task()),
        ]
        
        # 等待指定时长
        await asyncio.sleep(duration_hours * 3600)
        
        # 取消所有任务
        for task in tasks:
            task.cancel()
        
        # 等待任务清理
        await asyncio.gather(*tasks, return_exceptions=True)
        
    finally:
        # 停止 fuzzer
        await runtime_client.stop_fuzzer(fuzzer_id)
    
    # 更新上下文
    ctx.metadata.update({
        "phase3_state_analyses": state_analyses,
        "phase3_needs_adjustment": needs_adjustment,
        "phase3_duration_hours": duration_hours,
    })
    
    return ctx
```

### 方案 2：使用异步生成器模式

```python
async def analyze_telemetry_stream_realtime(
    self,
    telemetry_reader: Any,
    duration: timedelta,
    window_interval: timedelta = timedelta(minutes=1),
) -> AsyncGenerator[PerformanceMetrics, None]:
    """改进的遥测分析器，使用异步生成器实现实时数据流"""
    start_time = datetime.now(timezone.utc)
    end_time = start_time + duration
    last_rotation = start_time
    
    while datetime.now(timezone.utc) < end_time:
        # 读取和处理遥测数据
        entries = await telemetry_reader.read_batch(max_entries=1000)
        for entry in entries:
            instance_id = entry.get("instance_id", "default")
            if instance_id not in self.aggregators:
                self.aggregators[instance_id] = TelemetryAggregator()
            self.aggregators[instance_id].process_telemetry_entry(entry)
        
        # 检查窗口轮转
        current_time = datetime.now(timezone.utc)
        if current_time - last_rotation >= window_interval:
            for instance_id, aggregator in self.aggregators.items():
                metrics = aggregator.rotate_window()
                metrics.instance_id = instance_id
                self.metrics_history.append(metrics)
                
                # 实时 yield 指标
                yield metrics
            
            last_rotation = current_time
        
        await asyncio.sleep(0.1)
```

### 方案 3：事件驱动架构

```python
class RealtimeTelemetryProcessor:
    """事件驱动的遥测处理器"""
    
    def __init__(self, llm_client: LiteLLMClient, ctx: CampaignContext):
        self.llm_client = llm_client
        self.ctx = ctx
        self.event_handlers = {
            "window_complete": self.on_window_complete,
            "anomaly_detected": self.on_anomaly_detected,
            "analysis_interval": self.on_analysis_interval,
        }
        self.last_llm_analysis = datetime.now(timezone.utc)
        self.metrics_buffer = deque(maxlen=10)  # 保存最近10个窗口
    
    async def on_window_complete(self, metrics: PerformanceMetrics):
        """窗口完成时的处理"""
        self.metrics_buffer.append(metrics)
        
        # 检查是否需要 LLM 分析
        current_time = datetime.now(timezone.utc)
        if current_time - self.last_llm_analysis >= timedelta(minutes=10):
            await self.trigger_llm_analysis()
    
    async def on_anomaly_detected(self, metrics: PerformanceMetrics, anomalies: list[str]):
        """检测到异常时立即触发 LLM 分析"""
        logger.warning(f"Anomalies detected: {anomalies}")
        analysis = await self._analyze_with_llm(metrics, anomalies)
        if analysis.needs_adjustment:
            await self.request_strategy_adjustment(analysis)
    
    async def trigger_llm_analysis(self):
        """触发 LLM 状态分析"""
        if not self.metrics_buffer:
            return
        
        # 使用最近的指标进行分析
        latest_metrics = self.metrics_buffer[-1]
        analysis = await self._analyze_with_llm(latest_metrics)
        
        self.last_llm_analysis = datetime.now(timezone.utc)
        return analysis
```

## 实施建议

1. **优先级**：采用方案1（并发任务架构），因为它改动最小且符合当前代码风格
2. **渐进式重构**：先修复实时响应问题，再考虑更复杂的事件驱动架构
3. **测试策略**：
   - 单元测试并发任务的协调
   - 集成测试 LLM 分析的实时性
   - 性能测试确保不影响 fuzzing 性能

## 配置建议

```yaml
phase3:
  telemetry:
    window_size_seconds: 60
    batch_size: 1000
  llm_analysis:
    interval_minutes: 10
    anomaly_threshold: 0.5
  performance:
    max_memory_mb: 500
    max_cpu_percent: 20  # 为 LLM 分析预留资源
```

## 监控指标

- LLM 分析延迟：从数据生成到分析完成的时间
- 实时响应率：10分钟内完成分析的比例
- 调整建议准确率：LLM 建议被采纳的比例