# FuzzLM-Agent 简化配置文件 - 研究原型版
# 复制此文件为 config.yaml 并根据您的环境调整配置

# LLM配置
llm:
  provider: "openrouter"
  api_key: "your-api-key"  # 设置环境变量 OPENROUTER_API_KEY 或在此处填入
  model: "anthropic/claude-3.5-sonnet"
  temperature: 0.1
  max_retries: 3

# gRPC 通信配置
grpc:
  address: "localhost:50051"

# 知识库配置
knowledge_base:
  path: "./data/knowledge_base.db"
  max_retrieved_experiences: 5

# ID生成配置
id_generation:
  campaign_format: "semantic"      # semantic | uuid | hybrid
  fuzzer_format: "semantic"       # semantic | uuid  
  shadow_format: "semantic"       # semantic | uuid
  include_timestamp: true         # 是否在ID中包含时间戳
  include_target_name: true       # Campaign ID是否包含目标名称
  uuid_length: 6                  # UUID部分长度 (4, 6, 8)

# 系统配置
system:
  workspace_root: "./workspace"
  default_duration_hours: 2.0
  shadow_coverage_threshold: 0.3  # 覆盖率低于此值时运行Shadow测试
  cleanup_workspace: false  # Campaign结束后是否清理工作空间

# 基本模糊测试参数
fuzzing:
  initial_corpus_size: 6  # 默认种子数量
  max_mutators: 20  # 最大mutator数量警告阈值