# ADR-001: LLM Generation gRPC Service Implementation Strategy

## Status

Accepted (Conditional Retention)

## Context

FuzzLM-Agent 的原始架构设计（参见 `docs/workflow.md`）定义了一个分离式的通信架构：
- 控制平面 (gRPC): 用于低频但关键的控制指令
- 数据平面 (共享内存): 用于高频的遥测数据流

根据设计，LLM 生成的 Mutator 代码应该通过独立的 gRPC 服务在 Orchestrator 和 Runtime 之间传递。为此，项目定义了 `llm_generation.proto` 协议文件，包含：
- `GenerateMutatorCode`: 请求生成 Mutator 代码
- `SubmitErrorFeedback`: 提交错误反馈用于修复

## Decision

**决策**: 有条件保留 `llm_generation.proto` 定义，但采用简化的实现策略。

**实际实现方式**:
- 在 Phase 1 策略生成阶段直接调用 LLM API
- 生成的代码通过 `RuntimeClient.custom_code` 字段传递
- Rust 端在 `strategy_builder.rs` 中直接处理编译和验证
- 不启动独立的 gRPC 服务

**保留原因**:
1. 体现完整的原始架构设计思路
2. 为未来可能的完整实现预留接口
3. 支持架构对比和性能评估实验
4. 保持研究的完整性

## Consequences

### 正面影响
- **开发效率**: 避免了额外的服务管理复杂性
- **性能**: 减少了网络调用开销
- **集成简单**: 复用现有的 RuntimeClient 通信机制
- **快速验证**: 能够快速验证 LLM 驱动的 Mutator 生成概念

### 负面影响
- **架构一致性**: 偏离了原始的分层架构设计
- **可扩展性**: 难以支持独立的 LLM 代码生成服务
- **维护负担**: 需要维护未使用的协议定义和客户端代码
- **潜在混淆**: 可能让开发者误解实际的代码执行路径

### 缓解措施
- 在 `llm_generation.proto` 文件头部添加明确的警告和说明
- 在 `CLAUDE.md` 中详细说明当前的实现策略
- 标记相关的 gRPC 客户端代码为实验性/未使用
- 在代码注释中指向实际的实现路径

## Implementation Notes

### 当前实现路径
```
Orchestrator (Phase 1) 
    → LLM API 调用
    → 生成代码存储在 strategy.custom_code
    → RuntimeClient.start_fuzzer() 传递
    → Rust strategy_builder.rs 编译验证
```

### 原始设计路径（未实现）
```
Orchestrator 
    → gRPC LLMCodeGeneration.GenerateMutatorCode()
    → 独立服务处理
    → gRPC 返回生成的代码
    → Runtime 验证沙箱
```

## References

- `docs/research_paper.md` - 研究论文（设计源头）
- `docs/workflow.md` - 工作流规范（设计源头）
- `fuzzlm_agent/orchestrator/phase1_strategy.py` - 实际实现位置
- `fuzzlm_agent/infrastructure/grpc/llm_generation.proto` - 协议定义

## Review Date

2025-01-29

## Decision Makers

- 项目研究团队
- 基于代码分析和架构评估做出