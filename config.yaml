infrastructure:
  grpc_server_address: 0.0.0.0:50051
  grpc_timeout: 10.0
  shared_memory_buffer_size: 67108864
  shared_memory_stream_name: champion_main
  fuzzing_duration_hours: 3.0
  llm_timeout: 30
  validation_timeout: 150
  compilation_timeout: 60
directories:
  base_dir: .
  data_dir: data
  workspace_dir: runtime/workspace
  output_dir: runtime/output
  temp_dir: runtime/temp
execution:
  target_path: 'runtime/workspace/libxml2_libafl_fuzzer'
  unlimited_mode: false
  shadow_validation_minutes: 10
  max_parallel_shadows: 3
  enable_probationary: true
  # LibAFL特定配置
  corpus_dir: 'runtime/workspace/libxml2_xml_seeds'
  max_input_size: 1048576  # 1MB
  coverage_map_size: 65536  # LibAFL标准映射大小
resources:
  max_memory_mb: 4096
  max_cpu_cores: 8
  max_concurrent_instances: 4
knowledge:
  enable_rag: true
  embedding_provider: ollama
  embedding_model: bge-m3
  embedding_api_base: http://************:11434
  rag_similarity_threshold: 0.7
  max_retrieved_experiences: 5
llm:
  provider: openrouter
  api_key: 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
  model: openrouter/qwen/qwen3-coder:free
  api_base: https://openrouter.ai/api/v1
  max_tokens: 262144
  max_output_tokens: 65536
  temperature: 0.7
  max_retries: 3
  retry_delay: 1
embedding:
  provider: ollama
  model: bge-m3:latest
  api_base: http://************:11434
  vector_dimension: 1024
  batch_size: 32
  timeout: 30
monitoring:
  monitoring_interval: 30
  log_level: info
  enable_telemetry_logging: true
  enable_performance_monitoring: true
validation:
  max_retries: 3
  sandbox_isolation: true
  enable_automatic_fixing: true
modules:
  reflection_coverage_stagnant_window: 10
  reflection_coverage_stagnant_threshold: 0.01
  reflection_health_consecutive_declines: 3
  reflection_periodic_interval_minutes: 30
  reflection_successful_threshold: 0.6
  reflection_default_confidence_level: 0.5
  statistical_analysis_min_sample_size: 3
  statistical_analysis_confidence_level: 0.95
  statistical_analysis_significance_threshold: 0.05
  phase_processor_phase0_timeout_seconds: 300
  phase_processor_default_timeout_seconds: 600
  experience_manager_min_quality_score: 0.0
  experience_manager_default_query_limit: 10
system:
  use_real_fuzzing: true
  workspace_root: runtime/workspace
fuzzing:
  strategy_id: libxml2_adaptive_strategy
  benchmark_name: libxml2_xml
  target_type: xml_parser
  scheduler_config:
    type: weighted_random
    weights:
    - 0.4  # 新路径探索
    - 0.3  # 高频覆盖点
    - 0.2  # 复杂输入变异
    - 0.1  # 随机种子注入
    update_interval_seconds: 30
  timeout_seconds: 60
  initial_corpus_size: 10
  mutation_probability: 0.8
  coverage_weight: 0.6
  crash_weight: 0.9
  # LibXML2特定配置
  xml_specific:
    max_depth: 128       # 最大XML嵌套深度
    max_attributes: 64   # 最大属性数量
    enable_dtd: true     # 启用DTD处理
    enable_entities: true # 启用实体解析
