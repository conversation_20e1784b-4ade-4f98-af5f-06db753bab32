syntax = "proto3";

package telemetry;

// 遥测数据类型枚举
enum TelemetryDataType {
    UNKNOWN = 0;
    EXECUTION_COUNT = 1;
    COVERAGE_HIT = 2;
    CRASH_FOUND = 3;
    QUEUE_UPDATE = 4;
    ENERGY_UPDATE = 5;
    CORPUS_GROW = 6;
    HANG_FOUND = 7;
    MUTATOR_STATS = 8;
    SCHEDULER_STATS = 9;
    FEEDBACK_SCORE = 10;
}

// 遥测数据条目
message TelemetryEntry {
    TelemetryDataType data_type = 1;
    string instance_id = 2;
    uint64 timestamp_ns = 3;
    bytes data = 4;
}

// 执行计数数据
message ExecutionCountData {
    uint64 count = 1;
    float executions_per_second = 2;
}

// 覆盖率命中数据
message CoverageHitData {
    uint32 edge_id = 1;
    uint32 hit_count = 2;
    bool is_new = 3;
}

// 崩溃发现数据
message CrashFoundData {
    string crash_type = 1;
    bytes input_data = 2;
    string stack_trace = 3;
    int32 signal = 4;
}

// 语料库增长数据
message CorpusGrowData {
    uint32 corpus_size = 1;
    uint32 new_coverage = 2;
    bytes input_data = 3;
}

// 变异器统计数据
message MutatorStatsData {
    string mutator_name = 1;
    uint64 mutations_applied = 2;
    uint32 successful_mutations = 3;
    float success_rate = 4;
}

// 调度器统计数据
message SchedulerStatsData {
    string scheduler_name = 1;
    uint32 queue_size = 2;
    uint32 pending_entries = 3;
    float average_score = 4;
}

// 反馈分数数据
message FeedbackScoreData {
    string feedback_name = 1;
    float score = 2;
    bool is_interesting = 3;
}