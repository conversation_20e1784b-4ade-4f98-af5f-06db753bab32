use std::path::PathBuf;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 编译 libxml2 目标作为静态链接库
    compile_libxml2_target()?;

    // 新增：支持动态目标库链接
    setup_dynamic_target_linking()?;

    // 获取项目根目录
    let project_root = PathBuf::from(env!("CARGO_MANIFEST_DIR"))
        .parent()
        .unwrap()
        .parent()
        .unwrap()
        .to_path_buf();

    // Proto 文件路径
    let grpc_proto_dir = project_root.join("fuzzlm_agent/infrastructure/grpc");
    let shm_proto_dir = project_root.join("fuzzlm_agent/infrastructure/shared_memory");

    // 配置 gRPC 控制平面协议编译
    let mut grpc_config = prost_build::Config::new();
    grpc_config.type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]");

    // 编译 gRPC 控制平面协议
    tonic_build::configure()
        .build_server(true)
        .build_client(true)
        .compile_with_config(
            grpc_config,
            &[grpc_proto_dir.join("fuzzing_control.proto")],
            &[grpc_proto_dir.clone(), shm_proto_dir.clone()],
        )?;
    
    // 编译 LLM 代码生成协议
    let mut llm_config = prost_build::Config::new();
    llm_config.type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]");
    
    tonic_build::configure()
        .build_server(false)  // Rust端只需要客户端
        .build_client(true)
        .compile_with_config(
            llm_config,
            &[grpc_proto_dir.join("llm_generation.proto")],
            &[grpc_proto_dir.clone()],
        )?;

    // 编译 telemetry protobuf（不是 gRPC 服务）
    let mut telemetry_config = prost_build::Config::new();
    telemetry_config.type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]");
    
    prost_build::Config::new()
        .type_attribute(".", "#[derive(serde::Serialize, serde::Deserialize)]")
        .compile_protos(
            &[shm_proto_dir.join("telemetry.proto")],
            &[shm_proto_dir.clone()],
        )?;

    println!("cargo:rerun-if-changed=build.rs");
    println!(
        "cargo:rerun-if-changed={}/fuzzing_control.proto",
        grpc_proto_dir.display()
    );
    println!(
        "cargo:rerun-if-changed={}/llm_generation.proto",
        grpc_proto_dir.display()
    );
    println!(
        "cargo:rerun-if-changed={}/telemetry.proto",
        shm_proto_dir.display()
    );

    Ok(())
}

/// 编译 libxml2 目标作为静态链接库，使用LibAFL coverage instrumentation
fn compile_libxml2_target() -> Result<(), Box<dyn std::error::Error>> {
    println!("cargo:rustc-link-lib=xml2");

    // 使用pkg-config查找libxml2
    let pkg_config = pkg_config::Config::new();
    let libxml2 = pkg_config.probe("libxml-2.0").map_err(|e| {
        format!(
            "Failed to find libxml2 via pkg-config: {e}. Please install libxml2-dev/libxml2-devel"
        )
    })?;

    // 🔧 强制使用clang以支持LibAFL coverage instrumentation
    std::env::set_var("CC", "clang");
    std::env::set_var("CXX", "clang++");

    let mut build = cc::Build::new();
    build
        .file("src/libxml2_harness.c")
        .opt_level(3)
        .flag("-fPIC")
        .flag("-g")
        // 🎯 LibAFL关键：启用coverage instrumentation
        .flag("-fsanitize-coverage=trace-pc-guard,trace-cmp")
        // 🎯 LibAFL关键：确保符号可见性
        .flag("-fvisibility=default")
        // 🎯 LibAFL关键：禁用某些优化以保持instrumentation
        .flag("-fno-omit-frame-pointer")
        .flag("-Wno-sign-compare");

    // 添加libxml2的include路径
    for include_path in &libxml2.include_paths {
        build.include(include_path);
    }

    // 编译为静态库
    build.compile("xml2_harness");

    // 链接libxml2
    for lib_path in &libxml2.link_paths {
        println!("cargo:rustc-link-search=native={}", lib_path.display());
    }

    println!("cargo:rerun-if-changed=src/libxml2_harness.c");
    println!("cargo:rustc-link-lib=static=xml2_harness");

    Ok(())
}

/// 设置动态目标库链接支持
fn setup_dynamic_target_linking() -> Result<(), Box<dyn std::error::Error>> {
    // 获取项目根目录
    let project_root = PathBuf::from(env!("CARGO_MANIFEST_DIR"))
        .parent()
        .unwrap()
        .parent()
        .unwrap()
        .to_path_buf();

    // 目标库存储目录 - 使用新的runtime目录结构
    let target_libs_dir = project_root.join("runtime/workspace/target_libs");

    // 如果目标库目录存在，添加搜索路径
    if target_libs_dir.exists() {
        println!(
            "cargo:rustc-link-search=native={}",
            target_libs_dir.display()
        );
        println!("cargo:rerun-if-changed={}", target_libs_dir.display());

        // 扫描已编译的目标库
        if let Ok(entries) = std::fs::read_dir(&target_libs_dir) {
            for entry in entries.flatten() {
                let path = entry.path();
                if path.extension().is_some_and(|ext| ext == "a") {
                    if let Some(file_stem) = path.file_stem() {
                        if let Some(lib_name) = file_stem.to_str() {
                            // 移除 'lib' 前缀
                            if let Some(name) = lib_name.strip_prefix("lib") {
                                println!("cargo:rustc-link-lib=static={name}");
                                println!("cargo:rerun-if-changed={}", path.display());
                            }
                        }
                    }
                }
            }
        }
    }

    Ok(())
}
