//! 自动生成的 Protocol Buffers 代码

// gRPC 控制平面
pub mod fuzzing_control {
    tonic::include_proto!("fuzzing_control");
}

// LLM 代码生成协议
// 注意：这是实验性功能，当前未在生产中使用
// 实际的LLM代码生成通过 RuntimeClient.custom_code 字段传递
// 参见 docs/architecture/ADR-001-llm-generation-grpc-service.md
pub mod llm_generation {
    tonic::include_proto!("llm_generation");
}

// 遥测数据 protobuf 定义（用于跨语言兼容性）
pub mod telemetry {
    include!(concat!(env!("OUT_DIR"), "/fuzzlm.telemetry.rs"));
}

// 重新导出常用类型
pub use fuzzing_control::*;
pub use telemetry::*;
