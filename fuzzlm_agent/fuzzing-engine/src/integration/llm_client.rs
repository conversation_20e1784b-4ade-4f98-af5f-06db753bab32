//! LLM集成模块 - 处理与Python端LLM服务的通信
//! 
//! # ⚠️ 实验性代码警告 - 未在生产中使用
//! 
//! **当前状态**: 实验性/架构探索代码（未在实际运行时使用）
//! 
//! ## 背景说明
//! 
//! 本模块是FuzzLM-Agent原始架构设计的一部分，展示了通过独立gRPC服务进行LLM通信的完整方案。
//! 
//! ### 原始设计 vs 实际实现
//! 
//! **原始设计（本文件）**:
//! - 独立的 LLM 代码生成 gRPC 服务
//! - Orchestrator ↔ gRPC服务 ↔ Runtime 的完整分离架构
//! - 支持异步代码生成和错误反馈循环
//! 
//! **实际实现（已采用）**:
//! - Python端直接调用LLM API（见 `phase1_strategy.py`）
//! - 通过 `RuntimeClient.custom_code` 字段传递生成的代码
//! - 在 `strategy_builder.rs` 中直接编译和验证
//! 
//! ### 保留此代码的价值
//! 
//! 1. **架构参考**: 展示了完整的微服务架构设计思路
//! 2. **未来演进**: 当需要更高的可扩展性时，可以采用此架构
//! 3. **性能对比**: 可用于对比直接集成vs服务化架构的性能差异
//! 4. **研究价值**: 作为研究论文中"架构决策"章节的支撑材料
//! 
//! ### 实际使用的代码路径
//! 
//! 如果您想了解LLM集成的实际工作方式，请参考：
//! - Python端: `fuzzlm_agent/orchestrator/phase1_strategy.py`
//! - Rust端: `fuzzlm_agent/fuzzing-engine/src/strategy_builder.rs`
//! 
//! ### 相关文档
//! 
//! - `docs/architecture/ADR-001-llm-generation-grpc-service.md` - 架构决策记录
//! - `docs/architecture_evolution.md` - 架构演进说明（待创建）

use anyhow::{anyhow, Result};
use log::info;
use tonic::transport::Channel;
use uuid::Uuid;

// 导入生成的gRPC代码
pub mod llm_generation {
    tonic::include_proto!("llm_generation");
}

use llm_generation::{
    llm_code_generation_client::LlmCodeGenerationClient,
    MutatorGenerationRequest,
    ErrorFeedbackRequest,
};

/// LLM集成客户端
pub struct LLMIntegrationClient {
    client: LlmCodeGenerationClient<Channel>,
}

impl LLMIntegrationClient {
    /// 创建新的LLM集成客户端
    pub async fn new(llm_service_addr: &str) -> Result<Self> {
        info!("连接到LLM代码生成服务: {llm_service_addr}");
        
        let channel = Channel::from_shared(llm_service_addr.to_string())?
            .connect()
            .await?;
            
        let client = LlmCodeGenerationClient::new(channel);
        
        Ok(Self { client })
    }
    
    /// 请求生成Mutator代码
    pub async fn generate_mutator_code(
        &mut self,
        target_analysis: &str,
        strategy_config: &str,
        is_repair: bool,
        previous_error: Option<String>,
    ) -> Result<(String, llm_generation::MutatorMetadata)> {
        let request = MutatorGenerationRequest {
            request_id: Uuid::new_v4().to_string(),
            target_analysis: target_analysis.to_string(),
            strategy_config: strategy_config.to_string(),
            is_repair_attempt: is_repair,
            previous_error: previous_error.unwrap_or_default(),
        };
        
        info!("发送Mutator生成请求: {}", request.request_id);
        
        let response = self.client
            .generate_mutator_code(request)
            .await?
            .into_inner();
            
        if response.success {
            info!("✅ 成功接收生成的Mutator代码");
            Ok((
                response.generated_code,
                response.metadata.unwrap_or_default(),
            ))
        } else {
            Err(anyhow!("LLM代码生成失败: {}", response.error_message))
        }
    }
    
    /// 提交错误反馈
    pub async fn submit_error_feedback(
        &mut self,
        error_details: &str,
        validation_results: &str,
    ) -> Result<()> {
        let request = ErrorFeedbackRequest {
            request_id: Uuid::new_v4().to_string(),
            error_details: error_details.to_string(),
            validation_results: validation_results.to_string(),
        };
        
        info!("提交错误反馈: {}", request.request_id);
        
        let response = self.client
            .submit_error_feedback(request)
            .await?
            .into_inner();
            
        if response.acknowledged {
            Ok(())
        } else {
            Err(anyhow!("错误反馈未被确认"))
        }
    }
}