/*!
Phase 4.1: 高性能遥测数据发送模块 - 基于protobuf
专为高频fuzzing数据优化的批量发送和缓冲机制，现在使用protobuf格式
*/

use anyhow::{anyhow, Result};
use log::{debug, info, warn};
use std::sync::Arc;
use std::time::{Instant, SystemTime};
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, Duration};

use crate::telemetry_producer::TelemetryProducer;

#[derive(Debug, Clone)]
pub enum TelemetryTask {
    ExecutionStats {
        instance_id: String,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    },
    CoverageHit {
        instance_id: String,
        edge_id: u32,
        hit_count: u32,
        is_new: bool,
    },
    CrashFound {
        instance_id: String,
        crash_type: String,
        input_hash: u64,
        signal: u32,
    },
    CorpusGrow {
        instance_id: String,
        new_inputs: u32,
        total_size: u32,
        avg_length: u32,
    },
    MutatorStats {
        instance_id: String,
        mutator_id: u32,
        usage_count: u32,
        success_rate: f32,
    },
}

/// 高性能遥测数据缓冲器 - 专为高频数据优化
pub struct HighPerformanceTelemetryBuffer {
    max_buffer_size: usize,
    #[allow(dead_code)]
    batch_size: usize,
    #[allow(dead_code)]
    flush_interval_ms: u64,
    telemetry_producer: Arc<TelemetryProducer>,
    sender: mpsc::UnboundedSender<TelemetryTask>,
    stats: Arc<RwLock<BufferStats>>,
}

#[derive(Debug, Default, Clone)]
pub struct BufferStats {
    pub entries_buffered: u64,
    pub entries_sent: u64,
    pub batches_sent: u64,
    pub buffer_overflows: u64,
    pub send_errors: u64,
    pub avg_batch_size: f64,
    pub last_flush_time: Option<SystemTime>,
    pub buffer_utilization: f64,
}

impl HighPerformanceTelemetryBuffer {
    /// 创建高性能遥测缓冲器
    pub async fn new(
        telemetry_producer: Arc<TelemetryProducer>,
        max_buffer_size: usize,
        batch_size: usize,
        flush_interval_ms: u64,
    ) -> Result<Arc<Self>> {
        let stats = Arc::new(RwLock::new(BufferStats::default()));

        // 创建异步发送通道
        let (sender, mut receiver) = mpsc::unbounded_channel::<TelemetryTask>();

        let stats_clone = Arc::clone(&stats);
        let producer_clone = Arc::clone(&telemetry_producer);

        // 创建self的Arc引用
        let buffer = Arc::new(Self {
            max_buffer_size,
            batch_size,
            flush_interval_ms,
            telemetry_producer,
            sender,
            stats,
        });

        // 启动批量发送任务
        tokio::spawn(async move {
            Self::batch_sender_task(
                stats_clone,
                producer_clone,
                batch_size,
                flush_interval_ms,
                &mut receiver,
            )
            .await;
        });

        info!(
            "✓ 高性能遥测缓冲器初始化完成 - 缓冲区: {max_buffer_size}, 批量: {batch_size}, 刷新间隔: {flush_interval_ms}ms (protobuf)"
        );

        Ok(buffer)
    }

    /// 批量发送任务 - 在后台异步处理
    async fn batch_sender_task(
        stats: Arc<RwLock<BufferStats>>,
        producer: Arc<TelemetryProducer>,
        batch_size: usize,
        flush_interval_ms: u64,
        receiver: &mut mpsc::UnboundedReceiver<TelemetryTask>,
    ) {
        let mut flush_timer = interval(Duration::from_millis(flush_interval_ms));
        let mut pending_tasks = Vec::with_capacity(batch_size);

        info!("启动高性能遥测批量发送任务 (protobuf)");

        loop {
            tokio::select! {
                // 接收新的遥测任务
                task_opt = receiver.recv() => {
                    match task_opt {
                        Some(task) => {
                            pending_tasks.push(task);

                            // 缓冲区满时立即发送
                            if pending_tasks.len() >= batch_size {
                                Self::flush_batch(&pending_tasks, &producer, &stats).await;
                                pending_tasks.clear();
                            }
                        }
                        None => {
                            warn!("遥测发送通道关闭，退出批量发送任务");
                            break;
                        }
                    }
                }

                // 定时刷新
                _ = flush_timer.tick() => {
                    if !pending_tasks.is_empty() {
                        Self::flush_batch(&pending_tasks, &producer, &stats).await;
                        pending_tasks.clear();
                    }
                }
            }
        }

        // 最后刷新剩余数据
        if !pending_tasks.is_empty() {
            Self::flush_batch(&pending_tasks, &producer, &stats).await;
        }

        info!("高性能遥测批量发送任务退出");
    }

    /// 刷新批量数据
    async fn flush_batch(
        tasks: &[TelemetryTask],
        producer: &TelemetryProducer,
        stats: &Arc<RwLock<BufferStats>>,
    ) {
        if tasks.is_empty() {
            return;
        }

        let batch_size = tasks.len();
        let start_time = Instant::now();
        let mut success_count = 0;

        // 批量发送所有任务
        for task in tasks {
            let result = match task {
                TelemetryTask::ExecutionStats {
                    instance_id,
                    executions,
                    exec_per_sec,
                    corpus_size,
                    crashes,
                } => {
                    producer
                        .send_execution_stats(
                            instance_id.clone(),
                            *executions,
                            *exec_per_sec,
                            *corpus_size,
                            *crashes,
                        )
                        .await;
                    Ok(()) as Result<()>
                }
                TelemetryTask::CoverageHit {
                    instance_id,
                    edge_id,
                    hit_count,
                    is_new,
                } => {
                    producer
                        .send_coverage_hit(instance_id.clone(), *edge_id, *hit_count, *is_new)
                        .await;
                    Ok(()) as Result<()>
                }
                TelemetryTask::CrashFound {
                    instance_id,
                    crash_type,
                    input_hash,
                    signal,
                } => {
                    producer
                        .send_crash_found(
                            instance_id.clone(),
                            crash_type.clone(),
                            *input_hash,
                            *signal,
                        )
                        .await;
                    Ok(())
                }
                TelemetryTask::CorpusGrow {
                    instance_id,
                    new_inputs,
                    total_size,
                    avg_length,
                } => {
                    producer
                        .send_corpus_grow(
                            instance_id.clone(),
                            *new_inputs,
                            *total_size,
                            *avg_length,
                        )
                        .await;
                    Ok(())
                }
                TelemetryTask::MutatorStats {
                    instance_id,
                    mutator_id,
                    usage_count,
                    success_rate,
                } => {
                    producer
                        .send_mutator_stats(
                            instance_id.clone(),
                            *mutator_id,
                            *usage_count,
                            *success_rate,
                        )
                        .await;
                    Ok(())
                }
            };

            match result {
                Ok(_) => success_count += 1,
                Err(e) => {
                    debug!("遥测任务发送失败: {:?} - {}", task, e);
                }
            }
        }

        let send_duration = start_time.elapsed();

        // 更新统计信息
        {
            let mut stats_guard = stats.write().await;
            stats_guard.batches_sent += 1;
            stats_guard.entries_sent += success_count as u64;
            if success_count < batch_size {
                stats_guard.send_errors += (batch_size - success_count) as u64;
            }
            stats_guard.last_flush_time = Some(SystemTime::now());

            // 更新平均批量大小
            let total_batches = stats_guard.batches_sent as f64;
            stats_guard.avg_batch_size = (stats_guard.avg_batch_size * (total_batches - 1.0)
                + batch_size as f64)
                / total_batches;
        }

        if success_count == batch_size {
            debug!(
                "批量发送成功: {} 条目, 耗时: {:.2}ms (protobuf)",
                batch_size,
                send_duration.as_millis()
            );
        } else {
            warn!(
                "批量发送部分失败: {}/{} 成功, 耗时: {:.2}ms (protobuf)",
                success_count,
                batch_size,
                send_duration.as_millis()
            );
        }
    }

    /// 高性能发送遥测任务 - 异步非阻塞
    pub async fn send_task(&self, task: TelemetryTask) -> Result<()> {
        // 更新缓冲区统计
        {
            let mut stats_guard = self.stats.write().await;
            stats_guard.entries_buffered += 1;

            // 生产级溢出检查 - 基于通道容量和性能指标
            let current_buffered = stats_guard.entries_buffered - stats_guard.entries_sent;
            if current_buffered > self.max_buffer_size as u64 {
                stats_guard.buffer_overflows += 1;
                warn!("遥测缓冲区估计溢出! 积压条目: {current_buffered}");
                // 不返回错误，允许继续发送以避免丢失重要数据
            }

            stats_guard.buffer_utilization = current_buffered as f64 / self.max_buffer_size as f64;
        }

        // 通过异步通道发送（非阻塞）
        self.sender
            .send(task)
            .map_err(|e| anyhow!("发送遥测任务到通道失败: {}", e))?;

        Ok(())
    }

    /// 快速发送执行统计 - 高频调用优化
    pub async fn send_execution_stats_fast(
        &self,
        instance_id: &str,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    ) -> Result<()> {
        let task = TelemetryTask::ExecutionStats {
            instance_id: instance_id.to_string(),
            executions,
            exec_per_sec,
            corpus_size,
            crashes,
        };

        self.send_task(task).await
    }

    /// 快速发送覆盖率命中 - 高频调用优化
    pub async fn send_coverage_hit_fast(
        &self,
        instance_id: &str,
        edge_id: u32,
        hit_count: u32,
        is_new: bool,
    ) -> Result<()> {
        let task = TelemetryTask::CoverageHit {
            instance_id: instance_id.to_string(),
            edge_id,
            hit_count,
            is_new,
        };

        self.send_task(task).await
    }

    /// 快速发送语料库增长 - 关键事件优化
    pub async fn send_corpus_grow_fast(
        &self,
        instance_id: &str,
        new_inputs: u32,
        total_size: u32,
        avg_length: u32,
    ) -> Result<()> {
        let task = TelemetryTask::CorpusGrow {
            instance_id: instance_id.to_string(),
            new_inputs,
            total_size,
            avg_length,
        };

        self.send_task(task).await
    }

    /// 快速发送崩溃发现 - 关键事件优化
    pub async fn send_crash_found_fast(
        &self,
        instance_id: &str,
        crash_type: &str,
        input_hash: u64,
        signal: u32,
    ) -> Result<()> {
        let task = TelemetryTask::CrashFound {
            instance_id: instance_id.to_string(),
            crash_type: crash_type.to_string(),
            input_hash,
            signal,
        };

        self.send_task(task).await
    }

    /// 快速发送变异器统计 - 性能监控优化
    pub async fn send_mutator_stats_fast(
        &self,
        instance_id: &str,
        mutator_id: u32,
        usage_count: u32,
        success_rate: f32,
    ) -> Result<()> {
        let task = TelemetryTask::MutatorStats {
            instance_id: instance_id.to_string(),
            mutator_id,
            usage_count,
            success_rate,
        };

        self.send_task(task).await
    }

    /// 强制刷新所有缓冲数据  
    pub async fn force_flush(&self) -> Result<()> {
        info!("强制刷新遥测缓冲区 (protobuf)");
        // 实际实现可以通过特殊消息或者信号来触发刷新
        // 这里简化为信息日志
        Ok(())
    }

    /// 获取缓冲器统计信息
    pub async fn get_stats(&self) -> BufferStats {
        self.stats.read().await.clone()
    }

    /// 获取性能摘要
    pub async fn get_performance_summary(&self) -> String {
        let stats = self.get_stats().await;
        let current_buffered = stats.entries_buffered.saturating_sub(stats.entries_sent);

        format!(
            "高性能遥测缓冲器统计 (protobuf):\\n\\\
             - 缓冲条目: {} (当前积压: {}/{})\\n\\\
             - 已发送条目: {} (批次: {})\\n\\\
             - 平均批量大小: {:.1}\\n\\\
             - 缓冲区利用率: {:.1}%\\n\\\
             - 溢出次数: {}, 发送错误: {}",
            stats.entries_buffered,
            current_buffered,
            self.max_buffer_size,
            stats.entries_sent,
            stats.batches_sent,
            stats.avg_batch_size,
            stats.buffer_utilization * 100.0,
            stats.buffer_overflows,
            stats.send_errors
        )
    }
}

/// 遥测集成助手 - 为fuzzing循环提供便利接口
pub struct TelemetryIntegrationHelper {
    buffer: Arc<HighPerformanceTelemetryBuffer>,
    instance_id: String,
    last_stats_time: Instant,
    stats_interval: Duration,
}

impl TelemetryIntegrationHelper {
    /// 创建遥测集成助手
    pub fn new(
        buffer: Arc<HighPerformanceTelemetryBuffer>,
        instance_id: String,
        stats_interval_secs: u64,
    ) -> Self {
        Self {
            buffer,
            instance_id,
            last_stats_time: Instant::now(),
            stats_interval: Duration::from_secs(stats_interval_secs),
        }
    }

    /// 记录fuzzing迭代统计 - 优化的高频调用
    pub async fn record_fuzzing_iteration(
        &mut self,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    ) {
        // 只在统计间隔后发送，避免过于频繁
        if self.last_stats_time.elapsed() >= self.stats_interval {
            if let Err(e) = self
                .buffer
                .send_execution_stats_fast(
                    &self.instance_id,
                    executions,
                    exec_per_sec,
                    corpus_size,
                    crashes,
                )
                .await
            {
                debug!("发送执行统计失败: {e}");
            }

            self.last_stats_time = Instant::now();
        }
    }

    /// 记录新路径发现 - 关键事件立即发送
    pub async fn record_new_path_found(&self, corpus_size: u32) {
        if let Err(e) = self
            .buffer
            .send_corpus_grow_fast(
                &self.instance_id,
                1, // 新增1个输入
                corpus_size,
                0, // 平均长度待计算
            )
            .await
        {
            debug!("发送语料库增长失败: {e}");
        }
    }

    /// 记录崩溃发现 - 关键事件立即发送
    pub async fn record_crash_found(&self, crash_type: &str, signal: u32) {
        let input_hash = self.generate_input_hash();

        if let Err(e) = self
            .buffer
            .send_crash_found_fast(&self.instance_id, crash_type, input_hash, signal)
            .await
        {
            debug!("发送崩溃发现失败: {e}");
        }
    }

    /// 记录覆盖率命中 - 高频但采样发送
    pub async fn record_coverage_hit(&self, edge_id: u32, hit_count: u32, is_new: bool) {
        // 只有新覆盖或者采样发送以减少频率
        if is_new || hit_count % 100 == 0 {
            if let Err(e) = self
                .buffer
                .send_coverage_hit_fast(&self.instance_id, edge_id, hit_count, is_new)
                .await
            {
                debug!("发送覆盖率命中失败: {e}");
            }
        }
    }

    /// 生成输入哈希
    fn generate_input_hash(&self) -> u64 {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        self.instance_id.hash(&mut hasher);
        SystemTime::now().hash(&mut hasher);
        hasher.finish()
    }
}