/*!
LibAFL Fuzzing Engine Library

工作流驱动的 LibAFL Fuzzing 引擎，提供：
- gRPC 控制平面服务
- 共享内存遥测数据生产
- 多实例管理和资源隔离
- Champion/Shadow 并行执行
*/

// 核心模块 - 简化后的架构
pub mod generated; // 自动生成的 Protocol Buffers 代码
pub mod grpc_server;
pub mod harness_launcher; // 支持预编译fuzzer的启动器
pub mod harnesses; // LibAFL harness实现集合
pub mod high_performance_telemetry; // Phase 4.1: 高性能遥测数据发送模块
pub mod instance_manager;
pub mod integration; // 集成模块
pub mod path_resolver; // 智能路径解析器
pub mod target_compiler;
pub mod telemetry_producer;
pub mod validation; // 验证模块
pub mod examples; // 示例模块

// 核心策略模块
pub mod config;
pub mod error;
pub mod execution; // 新增：专门的执行模块
pub mod metrics;
pub mod strategy;
pub mod strategy_builder; // 新的"通用工厂"实现

// 导出主要组件
pub use crate::grpc_server::start_grpc_server;
pub use crate::high_performance_telemetry::{
    HighPerformanceTelemetryBuffer, TelemetryIntegrationHelper,
}; // Phase 4.1
pub use crate::instance_manager::{FuzzerInstanceConfig, InstanceManager};
pub use crate::path_resolver::{LibAFLComponents, PathResolver};
pub use crate::target_compiler::{CompilationConfig, CompilationResult, UnifiedTargetCompiler};
pub use crate::telemetry_producer::TelemetryProducer;
pub use crate::validation::{CompoundValidationSandbox, ValidationConfig, ValidationServiceAdapter, ValidationResult, ValidationStep};

// 核心导出
pub use crate::strategy_builder::{
    run_basic_fuzzing_test, validate_strategy_from_json_config, StrategyBuilder,
};

// 兼容性导出
pub use crate::config::EngineConfig;
pub use crate::error::{EngineError, Result};
pub use crate::metrics::MetricsCollector;
pub use crate::strategy::LibAFLStrategyFactory;

/// 引擎版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 支持的策略类型
pub mod strategy_types {
    pub const QUEUE_SCHEDULER: &str = "QueueScheduler";
    pub const WEIGHTED_SCHEDULER: &str = "WeightedScheduler";
    pub const POWER_SCHEDULER: &str = "PowerScheduler";

    pub const MAX_MAP_FEEDBACK: &str = "MaxMapFeedback";
    pub const TIME_FEEDBACK: &str = "TimeFeedback";
    pub const CRASH_FEEDBACK: &str = "CrashFeedback";

    pub const HAVOC_MUTATOR: &str = "HavocMutator";
    pub const TOKENS_MUTATOR: &str = "TokensMutator";
    pub const MOPT_MUTATOR: &str = "MOPTMutator";
}

/// 重新导出常用类型
pub mod prelude {
    pub use crate::strategy_types::*;
    pub use crate::{
        CompilationConfig,
        CompilationResult, // 🔧 编译器相关类型
        EngineConfig,
        EngineError,
        LibAFLComponents,
        LibAFLStrategyFactory,
        MetricsCollector,
        PathResolver,
        Result,
        UnifiedTargetCompiler,
    };
}
