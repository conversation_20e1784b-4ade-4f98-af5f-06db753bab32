"""FuzzLM-Agent: 基于LLM智能体的模块化模糊测试编排框架.

A revolutionary modular fuzzing strategy orchestration framework powered by LLM agents.
"""

import importlib

__version__ = "1.0.0-dev"
__author__ = "FuzzLM-Agent Team"
__email__ = "<EMAIL>"
__license__ = "MIT"


# 版本信息
def get_version() -> str:
    """获取当前版本信息."""
    return __version__


# 包初始化检查
def verify_dependencies() -> bool:
    """验证关键依赖是否正确安装."""
    required_packages = ["litellm", "pydantic", "typer"]

    missing_packages = []
    for package in required_packages:
        try:
            importlib.import_module(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        msg = f"缺少关键依赖: {', '.join(missing_packages)}"
        raise ImportError(msg)

    return True


__all__ = ["__version__", "get_version", "verify_dependencies"]
