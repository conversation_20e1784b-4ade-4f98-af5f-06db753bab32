"""Simple domain schemas for FuzzLM-Agent Research Prototype
========================================================

Minimal data structures needed for the research prototype.
No complex hierarchies, just essential models.
"""

from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime
from typing import Any


@dataclass
class CampaignConfig:
    """Campaign execution configuration."""

    target_path: str
    duration_hours: float = 2.0
    workspace_dir: str = "./workspace"


@dataclass
class FuzzingStrategy:
    """Simple fuzzing strategy configuration."""

    mutators: list[dict[str, Any]] = field(default_factory=list)
    scheduler: dict[str, Any] = field(default_factory=dict)
    feedback: dict[str, Any] = field(default_factory=dict)
    custom_code: str | None = None


@dataclass
class RuntimeMetrics:
    """Runtime performance metrics."""

    executions: int = 0
    coverage: float = 0.0
    crashes: int = 0
    unique_crashes: int = 0
    exec_per_sec: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class CampaignResult:
    """Simplified campaign result."""

    campaign_id: str
    success: bool
    start_time: datetime
    end_time: datetime
    final_coverage: float
    unique_crashes: int
    total_executions: int
    strategy_changes: int = 0
    phases_completed: int = 0
    error_message: str | None = None
    metadata: dict[str, Any] = field(default_factory=dict)

    @property
    def duration_hours(self) -> float:
        """Calculate duration in hours."""
        delta = self.end_time - self.start_time
        return delta.total_seconds() / 3600
