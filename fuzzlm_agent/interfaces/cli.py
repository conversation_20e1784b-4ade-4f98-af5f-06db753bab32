"""FuzzLM-Agent CLI接口 - 简化研究原型版
=====================================

基于CampaignOrchestrator的极简CLI
直接执行，无事件总线，专注核心功能
"""

from __future__ import annotations

import argparse
import logging
import sys
import traceback
from pathlib import Path
from typing import Any

from rich.console import Console
from rich.panel import Panel
from rich.progress import (
    Progress,
    SpinnerColumn,
    TextColumn,
    TimeElapsedColumn,
)
from rich.table import Table

from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignOrchestrator


class FuzzLMCLI:
    """FuzzLM-Agent 简化CLI - 研究原型版"""

    def __init__(self) -> None:
        self.console = Console()
        self.orchestrator: CampaignOrchestrator | None = None
        self._setup_logging()

    def _setup_logging(self) -> None:
        """设置日志系统"""
        log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        logging.basicConfig(
            format=log_format,
            level=logging.INFO,
            handlers=[logging.StreamHandler(sys.stdout)],
        )

    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="FuzzLM-Agent - 智能模糊测试框架（研究原型）",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
示例:
  python -m fuzzlm_agent start target.c                    # 运行2小时
  python -m fuzzlm_agent start target.c --duration 4.0    # 运行4小时
  python -m fuzzlm_agent start target.c --unlimited       # 无限运行
            """,
        )

        # 子命令
        subparsers = parser.add_subparsers(dest="command", help="可用命令")

        # start 命令
        start_parser = subparsers.add_parser("start", help="开始模糊测试Campaign")
        start_parser.add_argument("target_path", help="目标程序路径")
        start_parser.add_argument(
            "--duration",
            type=float,
            default=2.0,
            help="运行时长（小时），默认2小时",
        )
        start_parser.add_argument("--unlimited", action="store_true", help="无限期运行")
        start_parser.add_argument(
            "--config",
            default="config.yaml",
            help="配置文件路径",
        )

        return parser

    def run(self, args: list[str] | None = None) -> None:
        """运行CLI（同步接口）"""
        parser = self.create_parser()
        parsed_args = parser.parse_args(args)

        if not parsed_args.command:
            parser.print_help()
            return

        try:
            if parsed_args.command == "start":
                self.handle_start_command(parsed_args)
            else:
                self.console.print(f"[red]未知命令: {parsed_args.command}[/red]")

        except KeyboardInterrupt:
            self.console.print("\n[yellow]用户中断操作[/yellow]")
        except (RuntimeError, ValueError, OSError) as e:
            self.console.print(f"[red]错误: {e}[/red]")
            if logging.getLogger().level == logging.DEBUG:
                self.console.print(traceback.format_exc())

    def handle_start_command(self, args: Any) -> None:
        """处理start命令（同步）"""
        target_path = Path(args.target_path)

        # 验证目标路径
        if not target_path.exists():
            self.console.print(f"[red]错误: 目标路径不存在: {target_path}[/red]")
            return

        # 显示启动信息
        startup_info = self._create_startup_panel(args, target_path)
        self.console.print(startup_info)

        # 创建编排器
        try:
            self.orchestrator = CampaignOrchestrator(args.config)
        except (RuntimeError, ValueError, OSError) as e:
            self.console.print(f"[red]初始化失败: {e}[/red]")
            return

        # 执行Campaign
        duration = float("inf") if args.unlimited else args.duration

        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            TimeElapsedColumn(),
            console=self.console,
        ) as progress:
            task = progress.add_task("[cyan]执行模糊测试Campaign...[/cyan]", total=None)

            try:
                # 同步执行Campaign
                result = self.orchestrator.run_campaign(str(target_path), duration)

                progress.update(task, description="[green]Campaign执行完成[/green]")

                # 显示结果
                self._display_campaign_results(result)

            except Exception as e:
                progress.update(task, description="[red]Campaign执行失败[/red]")
                self.console.print(f"[red]执行错误: {e}[/red]")
                raise

    def _create_startup_panel(self, args: Any, target_path: Path) -> Panel:
        """创建启动信息面板"""
        mode_text = "无限期" if args.unlimited else f"{args.duration}小时"

        content = f"""[bold cyan]FuzzLM-Agent 研究原型[/bold cyan]
目标: {target_path}
模式: {mode_text}
配置: {args.config}
架构: CampaignOrchestrator (Phase 0-5)"""

        return Panel(content, title="🚀 启动信息", border_style="cyan")

    def _display_campaign_results(self, result: Any) -> None:
        """显示Campaign结果"""
        # 创建结果表格
        table = Table(title="Campaign执行结果")
        table.add_column("指标", style="cyan")
        table.add_column("值", style="green")

        # 基本信息
        table.add_row("Campaign ID", result.campaign_id[:8] + "...")
        table.add_row("状态", "✓ 成功" if result.success else "✗ 失败")
        table.add_row("持续时间", f"{result.duration_hours:.2f}小时")

        # 性能指标
        table.add_row("覆盖率", f"{result.final_coverage:.2%}")
        table.add_row("崩溃数", str(result.unique_crashes))
        table.add_row("执行次数", f"{result.total_executions:,}")
        table.add_row("策略变更", str(result.strategy_changes))
        table.add_row("完成阶段", f"{result.phases_completed}/6")

        self.console.print(table)

        # 总结
        if result.success:
            if result.unique_crashes > 0:
                summary = (
                    f"[green]✓ 成功完成，发现 {result.unique_crashes} 个崩溃[/green]"
                )
            else:
                summary = (
                    f"[green]✓ 成功完成，覆盖率 {result.final_coverage:.2%}[/green]"
                )
        else:
            summary = f"[red]✗ 执行失败: {result.error_message}[/red]"

        self.console.print(
            Panel(
                summary,
                title="总结",
                border_style="green" if result.success else "red",
            ),
        )


def main() -> None:
    """CLI入口点"""
    cli = FuzzLMCLI()
    cli.run()


if __name__ == "__main__":
    main()
