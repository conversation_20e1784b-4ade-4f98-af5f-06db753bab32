"""RuntimeClient - 真实的gRPC通信客户端
===================================

真实的gRPC客户端实现，用于与Rust fuzzing engine通信。
完全基于生成的gRPC stub，无任何模拟数据。
"""

from __future__ import annotations

import logging
from typing import Any

import grpc

from fuzzlm_agent.infrastructure.grpc import (
    fuzzing_control_pb2,
    fuzzing_control_pb2_grpc,
)
from fuzzlm_agent.infrastructure.id_generator import (
    generate_fuzzer_id,
    generate_shadow_id,
)

logger = logging.getLogger(__name__)


class RuntimeClient:
    """真实的gRPC运行时客户端 - 必须连接到实际的Rust fuzzing engine"""

    def __init__(self, config: dict[str, Any]):
        """初始化运行时客户端

        Args:
            config: 配置字典，包含server_address和timeout

        """
        self.server_address = config.get("server_address", "localhost:50051")
        self.timeout = config.get("timeout", 30.0)
        self.channel: grpc.aio.Channel | None = None
        self.stub: fuzzing_control_pb2_grpc.FuzzingControlStub | None = None
        self._connected = False

        logger.info(f"Runtime client initialized for: {self.server_address}")

    async def connect(self) -> None:
        """建立gRPC连接 - 必须成功连接才能使用"""
        if self._connected:
            return

        try:
            # 创建insecure channel
            self.channel = grpc.aio.insecure_channel(self.server_address)

            # 等待连接就绪
            await self.channel.channel_ready()

            # 创建stub
            self.stub = fuzzing_control_pb2_grpc.FuzzingControlStub(self.channel)

            # 验证连接
            health_req = fuzzing_control_pb2.HealthCheckRequest(  # type: ignore[attr-defined]
                include_system_metrics=True,
            )
            response = await self.stub.HealthCheck(health_req, timeout=self.timeout)

            if not response.healthy:
                msg = "Fuzzing engine is not healthy"
                raise RuntimeError(msg)

            self._connected = True
            logger.info(
                f"Successfully connected to gRPC server at {self.server_address}",
            )

        except grpc.RpcError as e:
            logger.error(f"gRPC connection failed: {e.code()} - {e.details()}")
            msg = f"Failed to connect to fuzzing engine at {self.server_address}: {e}"
            raise RuntimeError(msg) from e
        except Exception as e:
            logger.error(f"Failed to connect to gRPC server: {e}")
            msg = f"Failed to connect to fuzzing engine: {e}"
            raise RuntimeError(msg) from e

    async def disconnect(self) -> None:
        """断开gRPC连接"""
        if self.channel:
            await self.channel.close()
            self._connected = False
            self.stub = None
            logger.info("Disconnected from gRPC server")

    def _ensure_connected(self) -> None:
        """确保已连接，否则抛出异常"""
        if not self._connected or not self.stub:
            msg = "Not connected to fuzzing engine. Call connect() first."
            raise RuntimeError(msg)

    async def start_fuzzer(
        self,
        target_path: str,
        strategy: dict[str, Any],
        **kwargs: Any,
    ) -> str:
        """启动fuzzer实例

        Args:
            target_path: 目标程序路径
            strategy: 策略配置字典
            **kwargs: 额外参数（如fuzzer_type）

        Returns:
            Fuzzer实例ID

        """
        self._ensure_connected()
        assert self.stub is not None

        fuzzer_type = kwargs.get("fuzzer_type", "champion")
        instance_id = generate_fuzzer_id(fuzzer_type)
        target_library_path = kwargs.get("target_library_path", "")

        try:
            # 构建策略配置
            strategy_config = fuzzing_control_pb2.StrategyConfig(  # type: ignore[attr-defined]
                name=strategy.get("name", "default"),
                parameters={
                    k: str(v) for k, v in strategy.get("parameters", {}).items()
                },
            )

            # 如果有自定义代码，添加到参数中
            if "rust_mutator_code" in strategy:
                strategy_config.parameters["custom_code"] = strategy[
                    "rust_mutator_code"
                ]

            # 构建请求
            request = fuzzing_control_pb2.StartFuzzerRequest(  # type: ignore[attr-defined]
                instance_id=instance_id,
                fuzzer_type=(
                    fuzzing_control_pb2.FuzzerType.CHAMPION  # type: ignore[attr-defined]
                    if fuzzer_type == "champion"
                    else fuzzing_control_pb2.FuzzerType.SHADOW  # type: ignore[attr-defined]
                ),
                target_path=target_path,
                target_library_path=target_library_path,
                strategy=strategy_config,
            )

            # 发送请求
            response = await self.stub.StartFuzzer(request, timeout=self.timeout)

            if not response.success:
                msg = f"Failed to start fuzzer: {response.error_message}"
                raise RuntimeError(msg)

            logger.info(
                f"Started {fuzzer_type} fuzzer {response.instance_id} "
                f"(PID: {response.process_id})",
            )

            return str(response.instance_id)

        except grpc.RpcError as e:
            logger.error(f"gRPC error starting fuzzer: {e.code()} - {e.details()}")
            msg = f"Failed to start fuzzer: {e}"
            raise RuntimeError(msg) from e
        except Exception as e:
            logger.error(f"Failed to start fuzzer: {e}")
            raise

    async def stop_fuzzer(self, instance_id: str) -> bool:
        """停止fuzzer实例

        Args:
            instance_id: Fuzzer实例ID

        Returns:
            是否成功停止

        """
        self._ensure_connected()
        assert self.stub is not None

        try:
            request = fuzzing_control_pb2.StopFuzzerRequest(  # type: ignore[attr-defined]
                instance_id=instance_id,
                force=False,
            )

            response = await self.stub.StopFuzzer(request, timeout=self.timeout)

            if not response.success:
                logger.error(f"Failed to stop fuzzer: {response.error_message}")
                return False

            logger.info(f"Successfully stopped fuzzer {instance_id}")
            return True

        except grpc.RpcError as e:
            logger.error(f"gRPC error stopping fuzzer: {e.code()} - {e.details()}")
            return False
        except Exception as e:
            logger.error(f"Failed to stop fuzzer: {e}")
            return False

    async def get_status(self, instance_id: str) -> dict[str, Any]:
        """获取fuzzer状态

        Args:
            instance_id: Fuzzer实例ID

        Returns:
            状态信息字典

        """
        self._ensure_connected()
        assert self.stub is not None

        try:
            # 使用HealthCheck获取所有实例状态
            request = fuzzing_control_pb2.HealthCheckRequest(  # type: ignore[attr-defined]
                include_system_metrics=True,
            )

            response = await self.stub.HealthCheck(request, timeout=self.timeout)

            # 查找特定实例
            if instance_id in response.instances:
                instance = response.instances[instance_id]
                return {
                    "status": "running" if instance.is_running else "stopped",
                    "metrics": {
                        "executions": instance.metrics.executions_per_second
                        * instance.uptime_seconds,
                        "coverage": instance.metrics.coverage_edges / 10000.0,  # 归一化
                        "crashes": instance.metrics.crashes_found,
                        "unique_crashes": instance.metrics.crashes_found,  # 简化假设
                        "corpus_size": instance.metrics.corpus_size,
                        "exec_per_sec": instance.metrics.executions_per_second,
                    },
                }
            msg = f"Instance {instance_id} not found"
            raise RuntimeError(msg)

        except grpc.RpcError as e:
            logger.error(f"gRPC error getting status: {e.code()} - {e.details()}")
            msg = f"Failed to get fuzzer status: {e}"
            raise RuntimeError(msg) from e
        except Exception as e:
            logger.error(f"Failed to get fuzzer status: {e}")
            raise

    async def get_metrics(self, instance_id: str) -> dict[str, Any]:
        """获取fuzzer的详细性能指标

        Args:
            instance_id: Fuzzer实例ID

        Returns:
            详细的性能指标字典

        """
        self._ensure_connected()
        assert self.stub is not None

        try:
            # 使用HealthCheck获取指标
            request = fuzzing_control_pb2.HealthCheckRequest(  # type: ignore[attr-defined]
                include_system_metrics=True,
            )

            response = await self.stub.HealthCheck(request, timeout=self.timeout)

            # 查找特定实例
            if instance_id in response.instances:
                instance = response.instances[instance_id]
                metrics = instance.metrics

                return {
                    "executions": metrics.executions_per_second
                    * instance.uptime_seconds,
                    "coverage": metrics.coverage_edges / 10000.0,  # 归一化
                    "crashes": metrics.crashes_found,
                    "unique_crashes": metrics.crashes_found,  # 简化假设
                    "edges_found": metrics.coverage_edges,
                    "queue_size": metrics.corpus_size,
                    "exec_per_sec": metrics.executions_per_second,
                    "uptime_seconds": instance.uptime_seconds,
                    "process_id": instance.process_id,
                }
            msg = f"Instance {instance_id} not found"
            raise RuntimeError(msg)

        except grpc.RpcError as e:
            logger.error(f"gRPC error getting metrics: {e.code()} - {e.details()}")
            msg = f"Failed to get fuzzer metrics: {e}"
            raise RuntimeError(msg) from e
        except Exception as e:
            logger.error(f"Failed to get fuzzer metrics: {e}")
            raise

    async def update_strategy(self, instance_id: str, strategy: dict[str, Any]) -> bool:
        """更新fuzzer策略

        Args:
            instance_id: Fuzzer实例ID
            strategy: 新的策略配置

        Returns:
            是否成功更新

        """
        self._ensure_connected()
        assert self.stub is not None

        try:
            # 构建策略配置
            strategy_config = fuzzing_control_pb2.StrategyConfig(  # type: ignore[attr-defined]
                name=strategy.get("name", "updated"),
                parameters={
                    k: str(v) for k, v in strategy.get("parameters", {}).items()
                },
            )

            # 如果有自定义代码，添加到参数中
            if "rust_mutator_code" in strategy:
                strategy_config.parameters["custom_code"] = strategy[
                    "rust_mutator_code"
                ]

            request = fuzzing_control_pb2.UpdateStrategyRequest(  # type: ignore[attr-defined]
                instance_id=instance_id,
                new_strategy=strategy_config,
            )

            response = await self.stub.UpdateStrategy(request, timeout=self.timeout)

            if not response.success:
                logger.error(f"Failed to update strategy: {response.error_message}")
                return False

            logger.info(f"Successfully updated strategy for fuzzer {instance_id}")
            return True

        except grpc.RpcError as e:
            logger.error(f"gRPC error updating strategy: {e.code()} - {e.details()}")
            return False
        except Exception as e:
            logger.error(f"Failed to update strategy: {e}")
            return False

    async def spawn_shadow(
        self,
        champion_id: str,
        shadow_strategy: dict[str, Any],
    ) -> str | None:
        """从Champion创建Shadow fuzzer

        Args:
            champion_id: Champion fuzzer的实例ID
            shadow_strategy: Shadow使用的策略

        Returns:
            Shadow fuzzer的实例ID，失败返回None

        """
        self._ensure_connected()
        assert self.stub is not None

        shadow_id = generate_shadow_id()

        try:
            # 构建策略配置
            strategy_config = fuzzing_control_pb2.StrategyConfig(  # type: ignore[attr-defined]
                name=shadow_strategy.get("name", "shadow"),
                parameters={
                    k: str(v) for k, v in shadow_strategy.get("parameters", {}).items()
                },
            )

            # 如果有自定义代码，添加到参数中
            if "rust_mutator_code" in shadow_strategy:
                strategy_config.parameters["custom_code"] = shadow_strategy[
                    "rust_mutator_code"
                ]

            request = fuzzing_control_pb2.SpawnShadowRequest(  # type: ignore[attr-defined]
                champion_id=champion_id,
                shadow_id=shadow_id,
                shadow_strategy=strategy_config,
            )

            response = await self.stub.SpawnShadow(request, timeout=self.timeout)

            if not response.success:
                logger.error(f"Failed to spawn shadow: {response.error_message}")
                return None

            logger.info(
                f"Successfully spawned shadow {response.shadow_id} "
                f"(PID: {response.process_id}) from champion {champion_id}",
            )
            return str(response.shadow_id) if response.shadow_id else None

        except grpc.RpcError as e:
            logger.error(f"gRPC error spawning shadow: {e.code()} - {e.details()}")
            return None
        except Exception as e:
            logger.error(f"Failed to spawn shadow: {e}")
            return None

    async def promote_shadow(self, shadow_id: str) -> bool:
        """将Shadow提升为新的Champion

        Args:
            shadow_id: Shadow fuzzer的实例ID

        Returns:
            是否成功提升

        """
        self._ensure_connected()
        assert self.stub is not None

        try:
            # 查找当前的champion
            health_req = fuzzing_control_pb2.HealthCheckRequest()  # type: ignore[attr-defined]
            health_resp = await self.stub.HealthCheck(health_req, timeout=self.timeout)

            champion_id = None
            for inst_id, inst in health_resp.instances.items():
                if inst.fuzzer_type == fuzzing_control_pb2.FuzzerType.CHAMPION:  # type: ignore[attr-defined]
                    champion_id = inst_id
                    break

            if not champion_id:
                logger.error("No champion found to replace")
                return False

            request = fuzzing_control_pb2.PromoteShadowRequest(  # type: ignore[attr-defined]
                shadow_id=shadow_id,
                champion_id=champion_id,
                merge_corpus=True,
                terminate_champion=True,
            )

            response = await self.stub.PromoteShadow(request, timeout=self.timeout)

            if not response.success:
                logger.error(f"Failed to promote shadow: {response.error_message}")
                return False

            logger.info(
                f"Successfully promoted shadow {shadow_id} to champion "
                f"{response.new_champion_id}",
            )
            return True

        except grpc.RpcError as e:
            logger.error(f"gRPC error promoting shadow: {e.code()} - {e.details()}")
            return False
        except Exception as e:
            logger.error(f"Failed to promote shadow: {e}")
            return False

    async def validate_code(self, code: str, language: str = "rust") -> dict[str, Any]:
        """验证代码

        Args:
            code: 要验证的代码
            language: 编程语言（默认rust）

        Returns:
            验证结果字典

        """
        self._ensure_connected()
        assert self.stub is not None

        try:
            request = fuzzing_control_pb2.ValidateCodeRequest(  # type: ignore[attr-defined]
                code=code,
                language=language,
                validation_steps=["syntax", "compile", "behavior", "utility"],
            )

            response = await self.stub.ValidateCode(request, timeout=self.timeout)

            # 构建结果字典
            results = {}
            for result in response.results:
                results[result.step] = {
                    "passed": result.passed,
                    "message": result.message,
                    "metrics": dict(result.metrics),
                }

            return {
                "passed": response.passed,
                "results": results,
                "compiled_path": response.compiled_path,
            }

        except grpc.RpcError as e:
            logger.error(f"gRPC error validating code: {e.code()} - {e.details()}")
            msg = f"Failed to validate code: {e}"
            raise RuntimeError(msg) from e
        except Exception as e:
            logger.error(f"Failed to validate code: {e}")
            raise

    async def health_check(self) -> dict[str, Any]:
        """健康检查

        Returns:
            健康状态信息

        """
        if not self._connected:
            return {
                "status": "disconnected",
                "mode": "error",
                "message": "Not connected to fuzzing engine",
            }

        # 已连接状态下，stub 不应该为 None
        assert self.stub is not None

        try:
            request = fuzzing_control_pb2.HealthCheckRequest(  # type: ignore[attr-defined]
                include_system_metrics=True,
            )

            response = await self.stub.HealthCheck(request, timeout=self.timeout)

            return {
                "status": "healthy" if response.healthy else "unhealthy",
                "mode": "connected",
                "server": self.server_address,
                "instances": len(response.instances),
                "system_metrics": {
                    "cpu_usage": response.system_metrics.cpu_usage_percent,
                    "memory_used": response.system_metrics.memory_used_bytes,
                    "memory_total": response.system_metrics.memory_total_bytes,
                    "disk_usage": response.system_metrics.disk_usage_percent,
                },
            }

        except grpc.RpcError as e:
            logger.error(f"Health check failed: {e.code()} - {e.details()}")
            return {
                "status": "error",
                "mode": "connected",
                "message": f"gRPC error: {e.details()}",
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "error", "mode": "connected", "message": str(e)}


# 辅助函数
async def create_runtime_client(config: dict[str, Any]) -> RuntimeClient:
    """创建并连接运行时客户端

    Args:
        config: 配置字典

    Returns:
        已连接的RuntimeClient实例

    """
    client = RuntimeClient(config)
    await client.connect()
    return client
