# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: telemetry.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'telemetry.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ftelemetry.proto\x12\x10\x66uzzlm.telemetry\"\xb0\x05\n\x0eTelemetryEntry\x12\x36\n\tdata_type\x18\x01 \x01(\x0e\x32#.fuzzlm.telemetry.TelemetryDataType\x12\x13\n\x0binstance_id\x18\x02 \x01(\t\x12\x14\n\x0ctimestamp_ns\x18\x03 \x01(\x04\x12;\n\x0f\x65xecution_stats\x18\n \x01(\x0b\x32 .fuzzlm.telemetry.ExecutionStatsH\x00\x12\x35\n\x0c\x63overage_hit\x18\x0b \x01(\x0b\x32\x1d.fuzzlm.telemetry.CoverageHitH\x00\x12\x33\n\x0b\x63rash_found\x18\x0c \x01(\x0b\x32\x1c.fuzzlm.telemetry.CrashFoundH\x00\x12\x35\n\x0cqueue_update\x18\r \x01(\x0b\x32\x1d.fuzzlm.telemetry.QueueUpdateH\x00\x12\x37\n\renergy_update\x18\x0e \x01(\x0b\x32\x1e.fuzzlm.telemetry.EnergyUpdateH\x00\x12\x33\n\x0b\x63orpus_grow\x18\x0f \x01(\x0b\x32\x1c.fuzzlm.telemetry.CorpusGrowH\x00\x12\x31\n\nhang_found\x18\x10 \x01(\x0b\x32\x1b.fuzzlm.telemetry.HangFoundH\x00\x12\x37\n\rmutator_stats\x18\x11 \x01(\x0b\x32\x1e.fuzzlm.telemetry.MutatorStatsH\x00\x12;\n\x0fscheduler_stats\x18\x12 \x01(\x0b\x32 .fuzzlm.telemetry.SchedulerStatsH\x00\x12\x39\n\x0e\x66\x65\x65\x64\x62\x61\x63k_score\x18\x13 \x01(\x0b\x32\x1f.fuzzlm.telemetry.FeedbackScoreH\x00\x42\t\n\x07payload\"`\n\x0e\x45xecutionStats\x12\x12\n\nexecutions\x18\x01 \x01(\r\x12\x14\n\x0c\x65xec_per_sec\x18\x02 \x01(\x02\x12\x13\n\x0b\x63orpus_size\x18\x03 \x01(\r\x12\x0f\n\x07\x63rashes\x18\x04 \x01(\r\"A\n\x0b\x43overageHit\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\r\x12\x11\n\thit_count\x18\x02 \x01(\r\x12\x0e\n\x06is_new\x18\x03 \x01(\x08\"D\n\nCrashFound\x12\x12\n\ncrash_type\x18\x01 \x01(\t\x12\x12\n\ninput_hash\x18\x02 \x01(\x04\x12\x0e\n\x06signal\x18\x03 \x01(\r\"O\n\x0bQueueUpdate\x12\x12\n\nqueue_size\x18\x01 \x01(\r\x12\x17\n\x0fpending_favored\x18\x02 \x01(\r\x12\x13\n\x0b\x63ycles_done\x18\x03 \x01(\r\"M\n\x0c\x45nergyUpdate\x12\x0e\n\x06\x65nergy\x18\x01 \x01(\r\x12\x14\n\x0c\x65nergy_ratio\x18\x02 \x01(\x02\x12\x17\n\x0f\x65xecutions_left\x18\x03 \x01(\r\"H\n\nCorpusGrow\x12\x12\n\nnew_inputs\x18\x01 \x01(\r\x12\x12\n\ntotal_size\x18\x02 \x01(\r\x12\x12\n\navg_length\x18\x03 \x01(\r\"3\n\tHangFound\x12\x12\n\ninput_hash\x18\x01 \x01(\x04\x12\x12\n\ntimeout_ms\x18\x02 \x01(\r\"M\n\x0cMutatorStats\x12\x12\n\nmutator_id\x18\x01 \x01(\r\x12\x13\n\x0busage_count\x18\x02 \x01(\r\x12\x14\n\x0csuccess_rate\x18\x03 \x01(\x02\"Y\n\x0eSchedulerStats\x12\x14\n\x0cscheduler_id\x18\x01 \x01(\r\x12\x17\n\x0f\x63urrent_favored\x18\x02 \x01(\r\x12\x18\n\x10total_cal_cycles\x18\x03 \x01(\r\"F\n\rFeedbackScore\x12\x13\n\x0b\x66\x65\x65\x64\x62\x61\x63k_id\x18\x01 \x01(\r\x12\r\n\x05score\x18\x02 \x01(\x02\x12\x11\n\thit_count\x18\x03 \x01(\r\"q\n\x0eTelemetryBatch\x12\x31\n\x07\x65ntries\x18\x01 \x03(\x0b\x32 .fuzzlm.telemetry.TelemetryEntry\x12\x1a\n\x12\x62\x61tch_timestamp_ns\x18\x02 \x01(\x04\x12\x10\n\x08\x62\x61tch_id\x18\x03 \x01(\r*\xcd\x01\n\x11TelemetryDataType\x12\x13\n\x0f\x45XECUTION_COUNT\x10\x00\x12\x10\n\x0c\x43OVERAGE_HIT\x10\x01\x12\x0f\n\x0b\x43RASH_FOUND\x10\x02\x12\x10\n\x0cQUEUE_UPDATE\x10\x03\x12\x11\n\rENERGY_UPDATE\x10\x04\x12\x0f\n\x0b\x43ORPUS_GROW\x10\x05\x12\x0e\n\nHANG_FOUND\x10\x06\x12\x11\n\rMUTATOR_STATS\x10\x07\x12\x13\n\x0fSCHEDULER_STATS\x10\x08\x12\x12\n\x0e\x46\x45\x45\x44\x42\x41\x43K_SCORE\x10\tb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'telemetry_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_TELEMETRYDATATYPE']._serialized_start=1608
  _globals['_TELEMETRYDATATYPE']._serialized_end=1813
  _globals['_TELEMETRYENTRY']._serialized_start=38
  _globals['_TELEMETRYENTRY']._serialized_end=726
  _globals['_EXECUTIONSTATS']._serialized_start=728
  _globals['_EXECUTIONSTATS']._serialized_end=824
  _globals['_COVERAGEHIT']._serialized_start=826
  _globals['_COVERAGEHIT']._serialized_end=891
  _globals['_CRASHFOUND']._serialized_start=893
  _globals['_CRASHFOUND']._serialized_end=961
  _globals['_QUEUEUPDATE']._serialized_start=963
  _globals['_QUEUEUPDATE']._serialized_end=1042
  _globals['_ENERGYUPDATE']._serialized_start=1044
  _globals['_ENERGYUPDATE']._serialized_end=1121
  _globals['_CORPUSGROW']._serialized_start=1123
  _globals['_CORPUSGROW']._serialized_end=1195
  _globals['_HANGFOUND']._serialized_start=1197
  _globals['_HANGFOUND']._serialized_end=1248
  _globals['_MUTATORSTATS']._serialized_start=1250
  _globals['_MUTATORSTATS']._serialized_end=1327
  _globals['_SCHEDULERSTATS']._serialized_start=1329
  _globals['_SCHEDULERSTATS']._serialized_end=1418
  _globals['_FEEDBACKSCORE']._serialized_start=1420
  _globals['_FEEDBACKSCORE']._serialized_end=1490
  _globals['_TELEMETRYBATCH']._serialized_start=1492
  _globals['_TELEMETRYBATCH']._serialized_end=1605
# @@protoc_insertion_point(module_scope)
