# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: fuzzing_control.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'fuzzing_control.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15\x66uzzing_control.proto\x12\x0f\x66uzzing_control\"\xc0\x01\n\x12StartFuzzerRequest\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12\x30\n\x0b\x66uzzer_type\x18\x02 \x01(\x0e\x32\x1b.fuzzing_control.FuzzerType\x12\x13\n\x0btarget_path\x18\x03 \x01(\t\x12\x1b\n\x13target_library_path\x18\x04 \x01(\t\x12\x31\n\x08strategy\x18\x05 \x01(\x0b\x32\x1f.fuzzing_control.StrategyConfig\"f\n\x13StartFuzzerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x13\n\x0binstance_id\x18\x03 \x01(\t\x12\x12\n\nprocess_id\x18\x04 \x01(\x05\"7\n\x11StopFuzzerRequest\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12\r\n\x05\x66orce\x18\x02 \x01(\x08\"<\n\x12StopFuzzerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\"\x96\x01\n\x0eStrategyConfig\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x43\n\nparameters\x18\x02 \x03(\x0b\x32/.fuzzing_control.StrategyConfig.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"c\n\x15UpdateStrategyRequest\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12\x35\n\x0cnew_strategy\x18\x02 \x01(\x0b\x32\x1f.fuzzing_control.StrategyConfig\"@\n\x16UpdateStrategyResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\"v\n\x12SpawnShadowRequest\x12\x13\n\x0b\x63hampion_id\x18\x01 \x01(\t\x12\x11\n\tshadow_id\x18\x02 \x01(\t\x12\x38\n\x0fshadow_strategy\x18\x03 \x01(\x0b\x32\x1f.fuzzing_control.StrategyConfig\"d\n\x13SpawnShadowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x11\n\tshadow_id\x18\x03 \x01(\t\x12\x12\n\nprocess_id\x18\x04 \x01(\x05\"p\n\x14PromoteShadowRequest\x12\x11\n\tshadow_id\x18\x01 \x01(\t\x12\x13\n\x0b\x63hampion_id\x18\x02 \x01(\t\x12\x14\n\x0cmerge_corpus\x18\x03 \x01(\x08\x12\x1a\n\x12terminate_champion\x18\x04 \x01(\x08\"X\n\x15PromoteShadowResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x17\n\x0fnew_champion_id\x18\x03 \x01(\t\"O\n\x13ValidateCodeRequest\x12\x0c\n\x04\x63ode\x18\x01 \x01(\t\x12\x10\n\x08language\x18\x02 \x01(\t\x12\x18\n\x10validation_steps\x18\x03 \x03(\t\"q\n\x14ValidateCodeResponse\x12\x0e\n\x06passed\x18\x01 \x01(\x08\x12\x32\n\x07results\x18\x02 \x03(\x0b\x32!.fuzzing_control.ValidationResult\x12\x15\n\rcompiled_path\x18\x03 \x01(\t\"\xb2\x01\n\x10ValidationResult\x12\x0c\n\x04step\x18\x01 \x01(\t\x12\x0e\n\x06passed\x18\x02 \x01(\x08\x12\x0f\n\x07message\x18\x03 \x01(\t\x12?\n\x07metrics\x18\x04 \x03(\x0b\x32..fuzzing_control.ValidationResult.MetricsEntry\x1a.\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"4\n\x12HealthCheckRequest\x12\x1e\n\x16include_system_metrics\x18\x01 \x01(\x08\"\xf9\x01\n\x13HealthCheckResponse\x12\x0f\n\x07healthy\x18\x01 \x01(\x08\x12\x46\n\tinstances\x18\x02 \x03(\x0b\x32\x33.fuzzing_control.HealthCheckResponse.InstancesEntry\x12\x36\n\x0esystem_metrics\x18\x03 \x01(\x0b\x32\x1e.fuzzing_control.SystemMetrics\x1aQ\n\x0eInstancesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12.\n\x05value\x18\x02 \x01(\x0b\x32\x1f.fuzzing_control.InstanceStatus:\x02\x38\x01\"\xcd\x01\n\x0eInstanceStatus\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12\x30\n\x0b\x66uzzer_type\x18\x02 \x01(\x0e\x32\x1b.fuzzing_control.FuzzerType\x12\x12\n\nis_running\x18\x03 \x01(\x08\x12\x12\n\nprocess_id\x18\x04 \x01(\x05\x12\x16\n\x0euptime_seconds\x18\x05 \x01(\x03\x12\x34\n\x07metrics\x18\x06 \x01(\x0b\x32#.fuzzing_control.PerformanceMetrics\"w\n\x12PerformanceMetrics\x12\x1d\n\x15\x65xecutions_per_second\x18\x01 \x01(\x03\x12\x16\n\x0e\x63overage_edges\x18\x02 \x01(\x05\x12\x15\n\rcrashes_found\x18\x03 \x01(\x05\x12\x13\n\x0b\x63orpus_size\x18\x04 \x01(\x05\"}\n\rSystemMetrics\x12\x19\n\x11\x63pu_usage_percent\x18\x01 \x01(\x02\x12\x19\n\x11memory_used_bytes\x18\x02 \x01(\x03\x12\x1a\n\x12memory_total_bytes\x18\x03 \x01(\x03\x12\x1a\n\x12\x64isk_usage_percent\x18\x04 \x01(\x02\"U\n\x17GetChampionStateRequest\x12\x13\n\x0b\x63hampion_id\x18\x01 \x01(\t\x12\x13\n\x0bincremental\x18\x02 \x01(\x08\x12\x10\n\x08\x63ompress\x18\x03 \x01(\x08\"\xac\x01\n\x18GetChampionStateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x12\n\nstate_data\x18\x03 \x01(\x0c\x12\x15\n\ris_compressed\x18\x04 \x01(\x08\x12\x16\n\x0eis_incremental\x18\x05 \x01(\x08\x12\x12\n\nstate_size\x18\x06 \x01(\x03\x12\x11\n\ttimestamp\x18\x07 \x01(\x03\"]\n\x1bSyncIncrementalStateRequest\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12\x12\n\nstate_data\x18\x02 \x01(\x0c\x12\x15\n\ris_compressed\x18\x03 \x01(\x08\"\x92\x01\n\x1cSyncIncrementalStateResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x1c\n\x14\x63orpus_entries_added\x18\x03 \x01(\x05\x12\x16\n\x0enew_executions\x18\x04 \x01(\x03\x12\x14\n\x0cnew_coverage\x18\x05 \x01(\x05\"\xa2\x02\n\x1a\x45nhancedStartFuzzerRequest\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12\x30\n\x0b\x66uzzer_type\x18\x02 \x01(\x0e\x32\x1b.fuzzing_control.FuzzerType\x12\x13\n\x0btarget_path\x18\x03 \x01(\t\x12\x1b\n\x13target_library_path\x18\x04 \x01(\t\x12:\n\x0flegacy_strategy\x18\x05 \x01(\x0b\x32\x1f.fuzzing_control.StrategyConfigH\x00\x12<\n\rflat_strategy\x18\x06 \x01(\x0b\x32#.fuzzing_control.FlatStrategyConfigH\x00\x42\x11\n\x0fstrategy_config\"\xef\x01\n\x12\x46latStrategyConfig\x12\x0c\n\x04name\x18\x01 \x01(\t\x12T\n\x11global_parameters\x18\x02 \x03(\x0b\x32\x39.fuzzing_control.FlatStrategyConfig.GlobalParametersEntry\x12<\n\x0cinstructions\x18\x03 \x03(\x0b\x32&.fuzzing_control.FlatConfigInstruction\x1a\x37\n\x15GlobalParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xfd\x01\n\x15\x46latConfigInstruction\x12\x11\n\toperation\x18\x01 \x01(\t\x12\x16\n\x0e\x63omponent_type\x18\x02 \x01(\t\x12\x16\n\x0e\x63omponent_name\x18\x03 \x01(\t\x12J\n\nparameters\x18\x04 \x03(\x0b\x32\x36.fuzzing_control.FlatConfigInstruction.ParametersEntry\x12\x10\n\x08priority\x18\x05 \x01(\x05\x12\x10\n\x08optional\x18\x06 \x01(\x08\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"n\n\x1b\x45nhancedStartFuzzerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x13\n\x0binstance_id\x18\x03 \x01(\t\x12\x12\n\nprocess_id\x18\x04 \x01(\x05\"\xec\x01\n\x14\x43ompileTargetRequest\x12\x13\n\x0bsource_path\x18\x01 \x01(\t\x12.\n\x04mode\x18\x02 \x01(\x0e\x32 .fuzzing_control.CompilationMode\x12\x13\n\x0b\x65nable_asan\x18\x03 \x01(\x08\x12\x1e\n\x16\x65nable_libafl_coverage\x18\x04 \x01(\x08\x12\x15\n\rforce_rebuild\x18\x05 \x01(\x08\x12\x13\n\x0b\x65xtra_flags\x18\x06 \x03(\t\x12\x12\n\noutput_dir\x18\x07 \x01(\t\x12\x1a\n\x12optimization_level\x18\x08 \x01(\x05\"\xdd\x01\n\x15\x43ompileTargetResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x13\n\x0boutput_path\x18\x02 \x01(\t\x12\x18\n\x10\x63ompilation_time\x18\x03 \x01(\x01\x12\x17\n\x0f\x63ompiler_output\x18\x04 \x01(\t\x12\x17\n\x0f\x63ompiler_errors\x18\x05 \x01(\t\x12;\n\x0b\x63onfig_used\x18\x06 \x01(\x0b\x32&.fuzzing_control.CompilationConfigUsed\x12\x15\n\rerror_message\x18\x07 \x01(\t\"\x8c\x01\n\x15\x43ompilationConfigUsed\x12\x0c\n\x04mode\x18\x01 \x01(\x05\x12\x14\n\x0c\x61san_enabled\x18\x02 \x01(\x08\x12\x1f\n\x17libafl_coverage_enabled\x18\x03 \x01(\x08\x12\x1a\n\x12optimization_level\x18\x04 \x01(\x05\x12\x12\n\nflags_used\x18\x05 \x03(\t\"\x9d\x01\n\x1c\x41pplyFlatInstructionsRequest\x12\x13\n\x0binstance_id\x18\x01 \x01(\t\x12<\n\x0cinstructions\x18\x02 \x03(\x0b\x32&.fuzzing_control.FlatConfigInstruction\x12\x15\n\rvalidate_only\x18\x03 \x01(\x08\x12\x13\n\x0b\x66orce_apply\x18\x04 \x01(\x08\"\xa9\x01\n\x1d\x41pplyFlatInstructionsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rerror_message\x18\x02 \x01(\t\x12\x33\n\x07results\x18\x03 \x03(\x0b\x32\".fuzzing_control.InstructionResult\x12\x15\n\rapplied_count\x18\x04 \x01(\x05\x12\x14\n\x0c\x66\x61iled_count\x18\x05 \x01(\x05\"l\n\x11InstructionResult\x12\x19\n\x11instruction_index\x18\x01 \x01(\x05\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t\x12\x14\n\x0c\x63omponent_id\x18\x04 \x01(\t*&\n\nFuzzerType\x12\x0c\n\x08\x43HAMPION\x10\x00\x12\n\n\x06SHADOW\x10\x01*B\n\x0f\x43ompilationMode\x12\x0e\n\nDEBUG_MODE\x10\x00\x12\x10\n\x0cRELEASE_MODE\x10\x01\x12\r\n\tAUTO_MODE\x10\x02\x32\xbd\t\n\x0e\x46uzzingControl\x12X\n\x0bStartFuzzer\x12#.fuzzing_control.StartFuzzerRequest\x1a$.fuzzing_control.StartFuzzerResponse\x12U\n\nStopFuzzer\x12\".fuzzing_control.StopFuzzerRequest\x1a#.fuzzing_control.StopFuzzerResponse\x12\x61\n\x0eUpdateStrategy\x12&.fuzzing_control.UpdateStrategyRequest\x1a\'.fuzzing_control.UpdateStrategyResponse\x12X\n\x0bSpawnShadow\x12#.fuzzing_control.SpawnShadowRequest\x1a$.fuzzing_control.SpawnShadowResponse\x12^\n\rPromoteShadow\x12%.fuzzing_control.PromoteShadowRequest\x1a&.fuzzing_control.PromoteShadowResponse\x12[\n\x0cValidateCode\x12$.fuzzing_control.ValidateCodeRequest\x1a%.fuzzing_control.ValidateCodeResponse\x12X\n\x0bHealthCheck\x12#.fuzzing_control.HealthCheckRequest\x1a$.fuzzing_control.HealthCheckResponse\x12g\n\x10GetChampionState\x12(.fuzzing_control.GetChampionStateRequest\x1a).fuzzing_control.GetChampionStateResponse\x12s\n\x14SyncIncrementalState\x12,.fuzzing_control.SyncIncrementalStateRequest\x1a-.fuzzing_control.SyncIncrementalStateResponse\x12p\n\x13StartFuzzerEnhanced\x12+.fuzzing_control.EnhancedStartFuzzerRequest\x1a,.fuzzing_control.EnhancedStartFuzzerResponse\x12^\n\rCompileTarget\x12%.fuzzing_control.CompileTargetRequest\x1a&.fuzzing_control.CompileTargetResponse\x12v\n\x15\x41pplyFlatInstructions\x12-.fuzzing_control.ApplyFlatInstructionsRequest\x1a..fuzzing_control.ApplyFlatInstructionsResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'fuzzing_control_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_STRATEGYCONFIG_PARAMETERSENTRY']._loaded_options = None
  _globals['_STRATEGYCONFIG_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_VALIDATIONRESULT_METRICSENTRY']._loaded_options = None
  _globals['_VALIDATIONRESULT_METRICSENTRY']._serialized_options = b'8\001'
  _globals['_HEALTHCHECKRESPONSE_INSTANCESENTRY']._loaded_options = None
  _globals['_HEALTHCHECKRESPONSE_INSTANCESENTRY']._serialized_options = b'8\001'
  _globals['_FLATSTRATEGYCONFIG_GLOBALPARAMETERSENTRY']._loaded_options = None
  _globals['_FLATSTRATEGYCONFIG_GLOBALPARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_FLATCONFIGINSTRUCTION_PARAMETERSENTRY']._loaded_options = None
  _globals['_FLATCONFIGINSTRUCTION_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_FUZZERTYPE']._serialized_start=4802
  _globals['_FUZZERTYPE']._serialized_end=4840
  _globals['_COMPILATIONMODE']._serialized_start=4842
  _globals['_COMPILATIONMODE']._serialized_end=4908
  _globals['_STARTFUZZERREQUEST']._serialized_start=43
  _globals['_STARTFUZZERREQUEST']._serialized_end=235
  _globals['_STARTFUZZERRESPONSE']._serialized_start=237
  _globals['_STARTFUZZERRESPONSE']._serialized_end=339
  _globals['_STOPFUZZERREQUEST']._serialized_start=341
  _globals['_STOPFUZZERREQUEST']._serialized_end=396
  _globals['_STOPFUZZERRESPONSE']._serialized_start=398
  _globals['_STOPFUZZERRESPONSE']._serialized_end=458
  _globals['_STRATEGYCONFIG']._serialized_start=461
  _globals['_STRATEGYCONFIG']._serialized_end=611
  _globals['_STRATEGYCONFIG_PARAMETERSENTRY']._serialized_start=562
  _globals['_STRATEGYCONFIG_PARAMETERSENTRY']._serialized_end=611
  _globals['_UPDATESTRATEGYREQUEST']._serialized_start=613
  _globals['_UPDATESTRATEGYREQUEST']._serialized_end=712
  _globals['_UPDATESTRATEGYRESPONSE']._serialized_start=714
  _globals['_UPDATESTRATEGYRESPONSE']._serialized_end=778
  _globals['_SPAWNSHADOWREQUEST']._serialized_start=780
  _globals['_SPAWNSHADOWREQUEST']._serialized_end=898
  _globals['_SPAWNSHADOWRESPONSE']._serialized_start=900
  _globals['_SPAWNSHADOWRESPONSE']._serialized_end=1000
  _globals['_PROMOTESHADOWREQUEST']._serialized_start=1002
  _globals['_PROMOTESHADOWREQUEST']._serialized_end=1114
  _globals['_PROMOTESHADOWRESPONSE']._serialized_start=1116
  _globals['_PROMOTESHADOWRESPONSE']._serialized_end=1204
  _globals['_VALIDATECODEREQUEST']._serialized_start=1206
  _globals['_VALIDATECODEREQUEST']._serialized_end=1285
  _globals['_VALIDATECODERESPONSE']._serialized_start=1287
  _globals['_VALIDATECODERESPONSE']._serialized_end=1400
  _globals['_VALIDATIONRESULT']._serialized_start=1403
  _globals['_VALIDATIONRESULT']._serialized_end=1581
  _globals['_VALIDATIONRESULT_METRICSENTRY']._serialized_start=1535
  _globals['_VALIDATIONRESULT_METRICSENTRY']._serialized_end=1581
  _globals['_HEALTHCHECKREQUEST']._serialized_start=1583
  _globals['_HEALTHCHECKREQUEST']._serialized_end=1635
  _globals['_HEALTHCHECKRESPONSE']._serialized_start=1638
  _globals['_HEALTHCHECKRESPONSE']._serialized_end=1887
  _globals['_HEALTHCHECKRESPONSE_INSTANCESENTRY']._serialized_start=1806
  _globals['_HEALTHCHECKRESPONSE_INSTANCESENTRY']._serialized_end=1887
  _globals['_INSTANCESTATUS']._serialized_start=1890
  _globals['_INSTANCESTATUS']._serialized_end=2095
  _globals['_PERFORMANCEMETRICS']._serialized_start=2097
  _globals['_PERFORMANCEMETRICS']._serialized_end=2216
  _globals['_SYSTEMMETRICS']._serialized_start=2218
  _globals['_SYSTEMMETRICS']._serialized_end=2343
  _globals['_GETCHAMPIONSTATEREQUEST']._serialized_start=2345
  _globals['_GETCHAMPIONSTATEREQUEST']._serialized_end=2430
  _globals['_GETCHAMPIONSTATERESPONSE']._serialized_start=2433
  _globals['_GETCHAMPIONSTATERESPONSE']._serialized_end=2605
  _globals['_SYNCINCREMENTALSTATEREQUEST']._serialized_start=2607
  _globals['_SYNCINCREMENTALSTATEREQUEST']._serialized_end=2700
  _globals['_SYNCINCREMENTALSTATERESPONSE']._serialized_start=2703
  _globals['_SYNCINCREMENTALSTATERESPONSE']._serialized_end=2849
  _globals['_ENHANCEDSTARTFUZZERREQUEST']._serialized_start=2852
  _globals['_ENHANCEDSTARTFUZZERREQUEST']._serialized_end=3142
  _globals['_FLATSTRATEGYCONFIG']._serialized_start=3145
  _globals['_FLATSTRATEGYCONFIG']._serialized_end=3384
  _globals['_FLATSTRATEGYCONFIG_GLOBALPARAMETERSENTRY']._serialized_start=3329
  _globals['_FLATSTRATEGYCONFIG_GLOBALPARAMETERSENTRY']._serialized_end=3384
  _globals['_FLATCONFIGINSTRUCTION']._serialized_start=3387
  _globals['_FLATCONFIGINSTRUCTION']._serialized_end=3640
  _globals['_FLATCONFIGINSTRUCTION_PARAMETERSENTRY']._serialized_start=562
  _globals['_FLATCONFIGINSTRUCTION_PARAMETERSENTRY']._serialized_end=611
  _globals['_ENHANCEDSTARTFUZZERRESPONSE']._serialized_start=3642
  _globals['_ENHANCEDSTARTFUZZERRESPONSE']._serialized_end=3752
  _globals['_COMPILETARGETREQUEST']._serialized_start=3755
  _globals['_COMPILETARGETREQUEST']._serialized_end=3991
  _globals['_COMPILETARGETRESPONSE']._serialized_start=3994
  _globals['_COMPILETARGETRESPONSE']._serialized_end=4215
  _globals['_COMPILATIONCONFIGUSED']._serialized_start=4218
  _globals['_COMPILATIONCONFIGUSED']._serialized_end=4358
  _globals['_APPLYFLATINSTRUCTIONSREQUEST']._serialized_start=4361
  _globals['_APPLYFLATINSTRUCTIONSREQUEST']._serialized_end=4518
  _globals['_APPLYFLATINSTRUCTIONSRESPONSE']._serialized_start=4521
  _globals['_APPLYFLATINSTRUCTIONSRESPONSE']._serialized_end=4690
  _globals['_INSTRUCTIONRESULT']._serialized_start=4692
  _globals['_INSTRUCTIONRESULT']._serialized_end=4800
  _globals['_FUZZINGCONTROL']._serialized_start=4911
  _globals['_FUZZINGCONTROL']._serialized_end=6124
# @@protoc_insertion_point(module_scope)
