# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import fuzzing_control_pb2 as fuzzing__control__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in fuzzing_control_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class FuzzingControlStub(object):
    """FuzzLM-Agent 核心控制平面服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartFuzzer = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/StartFuzzer',
                request_serializer=fuzzing__control__pb2.StartFuzzerRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.StartFuzzerResponse.FromString,
                _registered_method=True)
        self.StopFuzzer = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/StopFuzzer',
                request_serializer=fuzzing__control__pb2.StopFuzzerRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.StopFuzzerResponse.FromString,
                _registered_method=True)
        self.UpdateStrategy = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/UpdateStrategy',
                request_serializer=fuzzing__control__pb2.UpdateStrategyRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.UpdateStrategyResponse.FromString,
                _registered_method=True)
        self.SpawnShadow = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/SpawnShadow',
                request_serializer=fuzzing__control__pb2.SpawnShadowRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.SpawnShadowResponse.FromString,
                _registered_method=True)
        self.PromoteShadow = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/PromoteShadow',
                request_serializer=fuzzing__control__pb2.PromoteShadowRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.PromoteShadowResponse.FromString,
                _registered_method=True)
        self.ValidateCode = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/ValidateCode',
                request_serializer=fuzzing__control__pb2.ValidateCodeRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.ValidateCodeResponse.FromString,
                _registered_method=True)
        self.HealthCheck = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/HealthCheck',
                request_serializer=fuzzing__control__pb2.HealthCheckRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.HealthCheckResponse.FromString,
                _registered_method=True)
        self.GetChampionState = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/GetChampionState',
                request_serializer=fuzzing__control__pb2.GetChampionStateRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.GetChampionStateResponse.FromString,
                _registered_method=True)
        self.SyncIncrementalState = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/SyncIncrementalState',
                request_serializer=fuzzing__control__pb2.SyncIncrementalStateRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.SyncIncrementalStateResponse.FromString,
                _registered_method=True)
        self.StartFuzzerEnhanced = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/StartFuzzerEnhanced',
                request_serializer=fuzzing__control__pb2.EnhancedStartFuzzerRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.EnhancedStartFuzzerResponse.FromString,
                _registered_method=True)
        self.CompileTarget = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/CompileTarget',
                request_serializer=fuzzing__control__pb2.CompileTargetRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.CompileTargetResponse.FromString,
                _registered_method=True)
        self.ApplyFlatInstructions = channel.unary_unary(
                '/fuzzing_control.FuzzingControl/ApplyFlatInstructions',
                request_serializer=fuzzing__control__pb2.ApplyFlatInstructionsRequest.SerializeToString,
                response_deserializer=fuzzing__control__pb2.ApplyFlatInstructionsResponse.FromString,
                _registered_method=True)


class FuzzingControlServicer(object):
    """FuzzLM-Agent 核心控制平面服务
    """

    def StartFuzzer(self, request, context):
        """启动 Fuzzer 实例
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopFuzzer(self, request, context):
        """停止 Fuzzer 实例
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateStrategy(self, request, context):
        """动态更新策略
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SpawnShadow(self, request, context):
        """创建 Shadow 实例
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PromoteShadow(self, request, context):
        """提升 Shadow 为 Champion
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ValidateCode(self, request, context):
        """验证代码
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HealthCheck(self, request, context):
        """健康检查
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetChampionState(self, request, context):
        """获取 Champion 状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SyncIncrementalState(self, request, context):
        """同步增量状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartFuzzerEnhanced(self, request, context):
        """增强版启动 Fuzzer
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CompileTarget(self, request, context):
        """编译目标
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ApplyFlatInstructions(self, request, context):
        """应用扁平化指令
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FuzzingControlServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartFuzzer': grpc.unary_unary_rpc_method_handler(
                    servicer.StartFuzzer,
                    request_deserializer=fuzzing__control__pb2.StartFuzzerRequest.FromString,
                    response_serializer=fuzzing__control__pb2.StartFuzzerResponse.SerializeToString,
            ),
            'StopFuzzer': grpc.unary_unary_rpc_method_handler(
                    servicer.StopFuzzer,
                    request_deserializer=fuzzing__control__pb2.StopFuzzerRequest.FromString,
                    response_serializer=fuzzing__control__pb2.StopFuzzerResponse.SerializeToString,
            ),
            'UpdateStrategy': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateStrategy,
                    request_deserializer=fuzzing__control__pb2.UpdateStrategyRequest.FromString,
                    response_serializer=fuzzing__control__pb2.UpdateStrategyResponse.SerializeToString,
            ),
            'SpawnShadow': grpc.unary_unary_rpc_method_handler(
                    servicer.SpawnShadow,
                    request_deserializer=fuzzing__control__pb2.SpawnShadowRequest.FromString,
                    response_serializer=fuzzing__control__pb2.SpawnShadowResponse.SerializeToString,
            ),
            'PromoteShadow': grpc.unary_unary_rpc_method_handler(
                    servicer.PromoteShadow,
                    request_deserializer=fuzzing__control__pb2.PromoteShadowRequest.FromString,
                    response_serializer=fuzzing__control__pb2.PromoteShadowResponse.SerializeToString,
            ),
            'ValidateCode': grpc.unary_unary_rpc_method_handler(
                    servicer.ValidateCode,
                    request_deserializer=fuzzing__control__pb2.ValidateCodeRequest.FromString,
                    response_serializer=fuzzing__control__pb2.ValidateCodeResponse.SerializeToString,
            ),
            'HealthCheck': grpc.unary_unary_rpc_method_handler(
                    servicer.HealthCheck,
                    request_deserializer=fuzzing__control__pb2.HealthCheckRequest.FromString,
                    response_serializer=fuzzing__control__pb2.HealthCheckResponse.SerializeToString,
            ),
            'GetChampionState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChampionState,
                    request_deserializer=fuzzing__control__pb2.GetChampionStateRequest.FromString,
                    response_serializer=fuzzing__control__pb2.GetChampionStateResponse.SerializeToString,
            ),
            'SyncIncrementalState': grpc.unary_unary_rpc_method_handler(
                    servicer.SyncIncrementalState,
                    request_deserializer=fuzzing__control__pb2.SyncIncrementalStateRequest.FromString,
                    response_serializer=fuzzing__control__pb2.SyncIncrementalStateResponse.SerializeToString,
            ),
            'StartFuzzerEnhanced': grpc.unary_unary_rpc_method_handler(
                    servicer.StartFuzzerEnhanced,
                    request_deserializer=fuzzing__control__pb2.EnhancedStartFuzzerRequest.FromString,
                    response_serializer=fuzzing__control__pb2.EnhancedStartFuzzerResponse.SerializeToString,
            ),
            'CompileTarget': grpc.unary_unary_rpc_method_handler(
                    servicer.CompileTarget,
                    request_deserializer=fuzzing__control__pb2.CompileTargetRequest.FromString,
                    response_serializer=fuzzing__control__pb2.CompileTargetResponse.SerializeToString,
            ),
            'ApplyFlatInstructions': grpc.unary_unary_rpc_method_handler(
                    servicer.ApplyFlatInstructions,
                    request_deserializer=fuzzing__control__pb2.ApplyFlatInstructionsRequest.FromString,
                    response_serializer=fuzzing__control__pb2.ApplyFlatInstructionsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'fuzzing_control.FuzzingControl', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('fuzzing_control.FuzzingControl', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class FuzzingControl(object):
    """FuzzLM-Agent 核心控制平面服务
    """

    @staticmethod
    def StartFuzzer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/StartFuzzer',
            fuzzing__control__pb2.StartFuzzerRequest.SerializeToString,
            fuzzing__control__pb2.StartFuzzerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopFuzzer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/StopFuzzer',
            fuzzing__control__pb2.StopFuzzerRequest.SerializeToString,
            fuzzing__control__pb2.StopFuzzerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateStrategy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/UpdateStrategy',
            fuzzing__control__pb2.UpdateStrategyRequest.SerializeToString,
            fuzzing__control__pb2.UpdateStrategyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SpawnShadow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/SpawnShadow',
            fuzzing__control__pb2.SpawnShadowRequest.SerializeToString,
            fuzzing__control__pb2.SpawnShadowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PromoteShadow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/PromoteShadow',
            fuzzing__control__pb2.PromoteShadowRequest.SerializeToString,
            fuzzing__control__pb2.PromoteShadowResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ValidateCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/ValidateCode',
            fuzzing__control__pb2.ValidateCodeRequest.SerializeToString,
            fuzzing__control__pb2.ValidateCodeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def HealthCheck(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/HealthCheck',
            fuzzing__control__pb2.HealthCheckRequest.SerializeToString,
            fuzzing__control__pb2.HealthCheckResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetChampionState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/GetChampionState',
            fuzzing__control__pb2.GetChampionStateRequest.SerializeToString,
            fuzzing__control__pb2.GetChampionStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SyncIncrementalState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/SyncIncrementalState',
            fuzzing__control__pb2.SyncIncrementalStateRequest.SerializeToString,
            fuzzing__control__pb2.SyncIncrementalStateResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartFuzzerEnhanced(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/StartFuzzerEnhanced',
            fuzzing__control__pb2.EnhancedStartFuzzerRequest.SerializeToString,
            fuzzing__control__pb2.EnhancedStartFuzzerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CompileTarget(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/CompileTarget',
            fuzzing__control__pb2.CompileTargetRequest.SerializeToString,
            fuzzing__control__pb2.CompileTargetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ApplyFlatInstructions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/fuzzing_control.FuzzingControl/ApplyFlatInstructions',
            fuzzing__control__pb2.ApplyFlatInstructionsRequest.SerializeToString,
            fuzzing__control__pb2.ApplyFlatInstructionsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
