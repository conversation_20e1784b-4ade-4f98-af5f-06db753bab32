// ==============================================================================
// 警告：实验性接口 - 当前未在生产环境使用
// ==============================================================================
// 
// 本协议定义了FuzzLM-Agent原始架构设计中的LLM代码生成服务接口。
// 
// 设计目的：
// - 支持动态生成定制化的LibAFL Mutator代码
// - 提供代码生成-验证-修复的迭代优化机制
// - 实现Python Orchestrator与Rust Runtime之间的分离式通信
//
// 当前状态（2025年1月）：
// - 协议定义完整但未实际部署
// - 实际实现采用了简化的直接集成方案（通过RuntimeClient.custom_code）
// - 保留此接口是为了：
//   1. 体现完整的原始架构设计思路
//   2. 为未来可能的完整实现预留接口
//   3. 支持架构对比和性能评估实验
//
// 如需了解当前的实际实现方式，请参考：
// - CLAUDE.md 中的"LLM-Driven Mutator Generation"章节
// - fuzzlm_agent/orchestrator/phase1_strategy.py
// ==============================================================================

syntax = "proto3";

package llm_generation;

// LLM代码生成服务
service LLMCodeGeneration {
    // 请求生成Mutator代码
    rpc GenerateMutatorCode(MutatorGenerationRequest) 
        returns (MutatorGenerationResponse);
    
    // 提交错误反馈用于修复
    rpc SubmitErrorFeedback(ErrorFeedbackRequest) 
        returns (ErrorFeedbackResponse);
}

message MutatorGenerationRequest {
    string request_id = 1;
    string target_analysis = 2;      // 目标程序分析结果（JSON）
    string strategy_config = 3;      // 策略配置（JSON）
    bool is_repair_attempt = 4;      // 是否为修复尝试
    string previous_error = 5;       // 上次错误信息（如果是修复）
}

message MutatorGenerationResponse {
    string request_id = 1;
    bool success = 2;
    string generated_code = 3;       // 生成的Rust代码
    string error_message = 4;
    MutatorMetadata metadata = 5;
}

message MutatorMetadata {
    string name = 1;
    string description = 2;
    repeated string target_types = 3;
    int32 complexity_level = 4;
}

message ErrorFeedbackRequest {
    string request_id = 1;
    string error_details = 2;
    string validation_results = 3;
}

message ErrorFeedbackResponse {
    string request_id = 1;
    bool acknowledged = 2;
}