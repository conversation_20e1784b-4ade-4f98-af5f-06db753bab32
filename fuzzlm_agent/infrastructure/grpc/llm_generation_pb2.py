# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: llm_generation.proto
# Protobuf Python Version: 6.31.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    0,
    '',
    'llm_generation.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14llm_generation.proto\x12\x0ellm_generation\"\x93\x01\n\x18MutatorGenerationRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x17\n\x0ftarget_analysis\x18\x02 \x01(\t\x12\x17\n\x0fstrategy_config\x18\x03 \x01(\t\x12\x19\n\x11is_repair_attempt\x18\x04 \x01(\x08\x12\x16\n\x0eprevious_error\x18\x05 \x01(\t\"\xa2\x01\n\x19MutatorGenerationResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x16\n\x0egenerated_code\x18\x03 \x01(\t\x12\x15\n\rerror_message\x18\x04 \x01(\t\x12\x31\n\x08metadata\x18\x05 \x01(\x0b\x32\x1f.llm_generation.MutatorMetadata\"d\n\x0fMutatorMetadata\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x14\n\x0ctarget_types\x18\x03 \x03(\t\x12\x18\n\x10\x63omplexity_level\x18\x04 \x01(\x05\"]\n\x14\x45rrorFeedbackRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x15\n\rerror_details\x18\x02 \x01(\t\x12\x1a\n\x12validation_results\x18\x03 \x01(\t\"A\n\x15\x45rrorFeedbackResponse\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x14\n\x0c\x61\x63knowledged\x18\x02 \x01(\x08\x32\xe3\x01\n\x11LLMCodeGeneration\x12j\n\x13GenerateMutatorCode\x12(.llm_generation.MutatorGenerationRequest\x1a).llm_generation.MutatorGenerationResponse\x12\x62\n\x13SubmitErrorFeedback\x12$.llm_generation.ErrorFeedbackRequest\x1a%.llm_generation.ErrorFeedbackResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'llm_generation_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_MUTATORGENERATIONREQUEST']._serialized_start=41
  _globals['_MUTATORGENERATIONREQUEST']._serialized_end=188
  _globals['_MUTATORGENERATIONRESPONSE']._serialized_start=191
  _globals['_MUTATORGENERATIONRESPONSE']._serialized_end=353
  _globals['_MUTATORMETADATA']._serialized_start=355
  _globals['_MUTATORMETADATA']._serialized_end=455
  _globals['_ERRORFEEDBACKREQUEST']._serialized_start=457
  _globals['_ERRORFEEDBACKREQUEST']._serialized_end=550
  _globals['_ERRORFEEDBACKRESPONSE']._serialized_start=552
  _globals['_ERRORFEEDBACKRESPONSE']._serialized_end=617
  _globals['_LLMCODEGENERATION']._serialized_start=620
  _globals['_LLMCODEGENERATION']._serialized_end=847
# @@protoc_insertion_point(module_scope)
