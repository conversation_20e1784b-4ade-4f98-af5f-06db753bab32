# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from . import llm_generation_pb2 as llm__generation__pb2

GRPC_GENERATED_VERSION = '1.73.1'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in llm_generation_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class LLMCodeGenerationStub(object):
    """LLM代码生成服务
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GenerateMutatorCode = channel.unary_unary(
                '/llm_generation.LLMCodeGeneration/GenerateMutatorCode',
                request_serializer=llm__generation__pb2.MutatorGenerationRequest.SerializeToString,
                response_deserializer=llm__generation__pb2.MutatorGenerationResponse.FromString,
                _registered_method=True)
        self.SubmitErrorFeedback = channel.unary_unary(
                '/llm_generation.LLMCodeGeneration/SubmitErrorFeedback',
                request_serializer=llm__generation__pb2.ErrorFeedbackRequest.SerializeToString,
                response_deserializer=llm__generation__pb2.ErrorFeedbackResponse.FromString,
                _registered_method=True)


class LLMCodeGenerationServicer(object):
    """LLM代码生成服务
    """

    def GenerateMutatorCode(self, request, context):
        """请求生成Mutator代码
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubmitErrorFeedback(self, request, context):
        """提交错误反馈用于修复
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_LLMCodeGenerationServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GenerateMutatorCode': grpc.unary_unary_rpc_method_handler(
                    servicer.GenerateMutatorCode,
                    request_deserializer=llm__generation__pb2.MutatorGenerationRequest.FromString,
                    response_serializer=llm__generation__pb2.MutatorGenerationResponse.SerializeToString,
            ),
            'SubmitErrorFeedback': grpc.unary_unary_rpc_method_handler(
                    servicer.SubmitErrorFeedback,
                    request_deserializer=llm__generation__pb2.ErrorFeedbackRequest.FromString,
                    response_serializer=llm__generation__pb2.ErrorFeedbackResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'llm_generation.LLMCodeGeneration', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('llm_generation.LLMCodeGeneration', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class LLMCodeGeneration(object):
    """LLM代码生成服务
    """

    @staticmethod
    def GenerateMutatorCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/llm_generation.LLMCodeGeneration/GenerateMutatorCode',
            llm__generation__pb2.MutatorGenerationRequest.SerializeToString,
            llm__generation__pb2.MutatorGenerationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def SubmitErrorFeedback(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/llm_generation.LLMCodeGeneration/SubmitErrorFeedback',
            llm__generation__pb2.ErrorFeedbackRequest.SerializeToString,
            llm__generation__pb2.ErrorFeedbackResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
