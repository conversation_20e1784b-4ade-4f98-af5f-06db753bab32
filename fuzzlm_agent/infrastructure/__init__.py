"""基础设施层
包含LLM客户端、运行时客户端和验证组件
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .litellm_client import LiteLLMClient


# 延迟导入以避免循环依赖
def __getattr__(name: str) -> type:
    if name == "LiteLLMClient":
        from .litellm_client import LiteLLMClient

        return LiteLLMClient
    msg = f"module '{__name__}' has no attribute '{name}'"
    raise AttributeError(msg)


__all__: list[str] = [
    "LiteLLMClient",
]
