#!/usr/bin/env python3
"""Phase 1: Hot-Start Initial Strategy Generation
==============================================

This module handles "hot-start" initial strategy generation as described in
docs/workflow.md Phase 1. It extracts target features, queries knowledge base
for similar experiences, and uses LLM to generate an optimized initial strategy.

This is a standalone, simplified implementation focused on research prototype
principles: simple > clever, direct > abstract.
"""

import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignContext
from fuzzlm_agent.prompts import PromptManager

logger = logging.getLogger(__name__)

# Initialize prompt manager (global instance for performance)
_prompt_manager = PromptManager()


async def phase1_generate_strategy(
    ctx: CampaignContext,  # CampaignContext
    llm_client: LiteLLMClient,  # LiteLLMClient
    knowledge_base: Any,  # SimpleKnowledgeBase
) -> CampaignContext:  # Returns updated CampaignContext
    """Generate initial fuzzing strategy using LLM and knowledge base.

    This function:
    1. Extracts target features from the source code
    2. Queries knowledge base for similar past experiences
    3. Builds a prompt with target features and similar cases
    4. Calls LLM to generate initial strategy
    5. Parses and validates the strategy response
    6. Updates ctx.strategy with the result

    Args:
        ctx: Campaign context containing target info and state
        llm_client: LLM client for strategy generation
        knowledge_base: Knowledge base for retrieving similar experiences

    Returns:
        Updated campaign context with generated strategy

    """
    logger.info(f"Phase 1: Starting strategy generation for {ctx.target_path}")

    try:
        # Step 1: Extract target features
        target_features = await _extract_target_features(ctx.target_path)
        logger.info(f"Extracted target features: {target_features}")

        # Step 2: Query knowledge base for similar experiences
        similar_experiences = await _query_similar_experiences(
            knowledge_base,
            target_features,
        )
        logger.info(f"Found {len(similar_experiences)} similar experiences")

        # Step 3: Build LLM prompt
        prompt = _build_strategy_prompt(
            target_features,
            similar_experiences,
            ctx.target_path,
        )

        # Step 4: Call LLM to generate strategy
        logger.info("Calling LLM for strategy generation...")
        llm_response = llm_client.generate(prompt)
        logger.info("LLM response received")

        # Step 5: Parse and validate strategy
        strategy = _parse_llm_strategy(llm_response, ctx)
        logger.info(f"Successfully parsed strategy: {strategy['name']}")

        # Step 5.5: Generate Rust mutator code if needed
        if ctx.config.get("enable_llm_mutator_generation", True):
            logger.info("Generating Rust mutator code...")
            rust_code = await _generate_rust_mutator_code(
                llm_client,
                strategy,
                target_features,
            )
            if rust_code:
                strategy["rust_mutator_code"] = rust_code
                logger.info("Successfully generated Rust mutator code")

        # Step 6: Update context with strategy
        ctx.strategy = strategy

        logger.info(f"Phase 1 completed successfully with strategy: {strategy['name']}")
        return ctx

    except Exception as e:
        logger.error(f"Phase 1 strategy generation failed: {e}")
        # Create fallback strategy
        logger.info("Creating fallback strategy...")
        fallback_strategy = _create_fallback_strategy(ctx)
        ctx.strategy = fallback_strategy
        return ctx


async def _extract_target_features(target_path: str) -> dict[str, Any]:
    """Extract features from target source code."""
    path = Path(target_path)

    # Basic feature extraction
    features: dict[str, Any] = {
        "file_type": "source" if path.suffix in [".c", ".cpp"] else "binary",
        "language": "c" if path.suffix == ".c" else "cpp",
        "file_name": path.name,
        "characteristics": [],
    }

    # Read file to extract more features
    try:
        if path.exists() and path.suffix in [".c", ".cpp"]:
            content = path.read_text()
            features["size_bytes"] = len(content)

            # Simple heuristic analysis
            if "malloc" in content or "free" in content:
                features["characteristics"].append("manual_memory_management")
            if "strcpy" in content or "strcat" in content:
                features["characteristics"].append("string_operations")
            if "fopen" in content or "fread" in content:
                features["characteristics"].append("file_io")
            if "pthread" in content:
                features["characteristics"].append("multithreading")
            if "socket" in content:
                features["characteristics"].append("network_operations")

            # Estimate complexity
            line_count = content.count("\n")
            if line_count < 100:
                features["complexity_level"] = "low"
            elif line_count < 500:
                features["complexity_level"] = "medium"
            else:
                features["complexity_level"] = "high"

    except Exception as e:
        logger.warning(f"Failed to read target file for analysis: {e}")
        features["complexity_level"] = "medium"

    return features


async def _query_similar_experiences(
    knowledge_base: Any,
    target_features: dict[str, Any],
) -> list[dict[str, Any]]:
    """Query knowledge base for similar fuzzing experiences."""
    try:
        if hasattr(knowledge_base, "search_experiences"):
            # 使用简化的经验查询，适应当前最小化架构
            return []
        # Simple fallback
        logger.warning("Knowledge base does not support experience search")
        return []
    except Exception as e:
        logger.warning(f"Failed to query knowledge base: {e}")
        return []


def _build_strategy_prompt(
    target_features: dict[str, Any],
    similar_experiences: list[dict[str, Any]],
    target_path: str,
) -> str:
    """Build prompt for LLM strategy generation using PromptManager."""
    # Format similar experiences summary
    experience_summary = ""
    if similar_experiences:
        experience_summary = "\nSimilar fuzzing campaigns:\n"
        for i, exp in enumerate(similar_experiences[:3]):
            metrics = exp.get("success_metrics", {})
            coverage = metrics.get("final_coverage", 0)
            crashes = metrics.get("unique_crashes", 0)
            experience_summary += (
                f"- Case {i + 1}: {coverage:.1%} coverage, {crashes} crashes found\n"
            )

    try:
        # Use PromptManager to generate the prompt
        return _prompt_manager.get_prompt(
            "strategy.generation",
            target_path=target_path,
            language=target_features.get("language", "unknown"),
            complexity_level=target_features.get("complexity_level", "medium"),
            characteristics=target_features.get("characteristics", []),
            experience_summary=experience_summary.strip(),
        )

    except Exception as e:
        logger.error(f"Failed to generate strategy prompt: {e}")
        # Fallback to basic prompt if template fails
        return f"""You are an expert fuzzing engineer. Generate an optimized initial fuzzing strategy for {Path(target_path).name}.

Target: {target_features.get("language", "unknown")} program with {target_features.get("complexity_level", "medium")} complexity.
Characteristics: {", ".join(target_features.get("characteristics", ["general"]))}

Generate a JSON response with strategy_name, components (scheduler, mutator, feedback, observer), and reasoning."""


def _parse_llm_strategy(response: str, ctx: CampaignContext) -> dict[str, Any]:
    """Parse LLM response into a strategy dict."""
    try:
        # Use json_repair directly
        import json_repair

        strategy_data = json_repair.loads(response)

        if not isinstance(strategy_data, dict):
            msg = "Failed to parse strategy from LLM response - not a JSON object"
            raise ValueError(msg)

        # Extract fields
        strategy_name = strategy_data.get(
            "strategy_name",
            f"LLM Strategy for {Path(ctx.target_path).stem}",
        )

        # Return simplified strategy dict
        return {
            "name": strategy_name,
            "mutators": strategy_data.get(
                "mutators",
                [
                    {"type": "havoc", "config": {"max_len": 1024}},
                    {"type": "splice", "config": {}},
                    {"type": "radamsa", "config": {}},
                ],
            ),
            "scheduler": strategy_data.get(
                "scheduler",
                {"type": "weighted", "config": {}},
            ),
            "feedback": strategy_data.get(
                "feedback",
                {"type": "max_map", "config": {}},
            ),
            "metadata": {
                "generation_method": "llm_driven",
                "generation_timestamp": datetime.now(timezone.utc).isoformat(),
                "llm_reasoning": strategy_data.get("reasoning", ""),
            },
        }

    except Exception as e:
        logger.error(f"Failed to parse LLM strategy: {e}")
        raise


def _create_fallback_strategy(ctx: Any) -> dict[str, Any]:
    """Create a simple fallback strategy."""
    target_name = Path(ctx.target_path).stem

    # Basic safe strategy
    return {
        "name": f"Fallback Strategy for {target_name}",
        "mutators": [
            {"type": "havoc", "config": {"max_len": 1024}},
            {"type": "splice", "config": {}},
        ],
        "scheduler": {"type": "weighted", "config": {}},
        "feedback": {"type": "max_map", "config": {}},
        "metadata": {
            "generation_method": "fallback",
            "generation_timestamp": datetime.now(timezone.utc).isoformat(),
        },
    }


async def _generate_rust_mutator_code(
    llm_client: Any,
    strategy: dict[str, Any],
    target_features: dict[str, Any],
) -> str:
    """Generate Rust mutator code based on the strategy.

    This is a minimal implementation for research prototype.
    It generates a basic custom mutator that can be compiled and loaded by LibAFL.
    """
    try:
        # Extract mutator types from strategy
        mutator_types = [m.get("type", "havoc") for m in strategy.get("mutators", [])]

        # Use PromptManager to generate the code generation prompt
        prompt = _prompt_manager.get_prompt(
            "code_generation.rust_mutator",
            mutator_types=mutator_types,
            characteristics=target_features.get("characteristics", ["general"]),
            complexity_level=target_features.get("complexity_level", "medium"),
        )

        # Call LLM to generate code
        logger.info("Calling LLM to generate Rust mutator code...")
        code_response = llm_client.generate(prompt)

        # Extract code from response
        code = _extract_rust_code(code_response)

        if not code:
            logger.warning("Failed to extract valid Rust code from LLM response")
            return ""

        logger.info(f"Generated Rust mutator code ({len(code)} chars)")
        return code

    except Exception as e:
        logger.error(f"Failed to generate Rust mutator code: {e}")
        # Fallback to basic prompt if template fails
        try:
            fallback_prompt = f"""Generate a LibAFL custom mutator in Rust for mutator types: {", ".join(mutator_types)}.

The code should implement the Mutator trait and be compilable as a dynamic library. Generate ONLY Rust code."""

            code_response = llm_client.generate(fallback_prompt)
            code = _extract_rust_code(code_response)
            return code if code else ""

        except Exception as fallback_error:
            logger.error(f"Fallback Rust code generation also failed: {fallback_error}")
            return ""


def _extract_rust_code(response: str) -> str:
    """Extract Rust code from LLM response."""
    # Look for code blocks
    if "```rust" in response:
        start = response.find("```rust") + 7
        end = response.find("```", start)
        if end > start:
            return response[start:end].strip()

    # Look for generic code blocks
    if "```" in response:
        start = response.find("```") + 3
        # Skip language identifier if present
        if (
            response[start : start + 10].strip()
            and "\n" in response[start : start + 10]
        ):
            start = response.find("\n", start) + 1
        end = response.find("```", start)
        if end > start:
            return response[start:end].strip()

    # If no code blocks, assume entire response is code
    # (but only if it looks like Rust code)
    if "impl" in response and "fn" in response:
        return response.strip()

    return ""
