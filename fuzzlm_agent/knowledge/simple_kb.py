"""SimpleKnowledgeBase - Minimal knowledge base using SQLite
~200 lines of simple, direct code
"""

from __future__ import annotations

import json
import logging
import math
import sqlite3
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

logger = logging.getLogger(__name__)


class SimpleKnowledgeBase:
    """Simple knowledge base using SQLite for experience storage"""

    def __init__(self, db_path: str = "data/knowledge.db"):
        """Initialize the knowledge base

        Args:
            db_path: Path to SQLite database file

        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize database
        self._init_db()

        logger.info("Initialized knowledge base: %s", self.db_path)

    def _init_db(self) -> None:
        """Initialize database schema"""
        conn = sqlite3.connect(str(self.db_path))
        try:
            conn.execute(
                """
                CREATE TABLE IF NOT EXISTS experiences (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT NOT NULL,
                    target_features TEXT,
                    strategy_config TEXT,
                    effectiveness_score REAL DEFAULT 0.0,
                    tags TEXT,
                    created_at TEXT NOT NULL,
                    embedding TEXT
                )
            """,
            )

            # Create indexes for faster searching
            conn.execute(
                """CREATE INDEX IF NOT EXISTS idx_effectiveness
                   ON experiences(effectiveness_score)""",
            )
            conn.execute(
                "CREATE INDEX IF NOT EXISTS idx_created ON experiences(created_at)",
            )

            conn.commit()
        finally:
            conn.close()

    def store_experience(
        self,
        experience_id: str,
        title: str,
        content: dict[str, Any],
        target_features: dict[str, Any],
        effectiveness_score: float = 0.0,
        tags: list[str] | None = None,
    ) -> str:
        """Store a new experience

        Args:
            experience_id: Unique identifier
            title: Experience title
            content: Experience content (strategy config, results, etc)
            target_features: Target characteristics
            effectiveness_score: How effective this experience was (0-1)
            tags: Optional tags for categorization

        Returns:
            The experience ID

        """
        conn = sqlite3.connect(str(self.db_path))
        try:
            # Generate simple embedding from features (just for demo)
            embedding = self._generate_simple_embedding(target_features, content)

            conn.execute(
                """
                INSERT OR REPLACE INTO experiences
                (id, title, content, target_features, effectiveness_score, tags, created_at, embedding)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """,
                (
                    experience_id,
                    title,
                    json.dumps(content),
                    json.dumps(target_features),
                    effectiveness_score,
                    json.dumps(tags or []),
                    datetime.now(timezone.utc).isoformat(),
                    json.dumps(embedding),
                ),
            )

            conn.commit()
            logger.info("Stored experience: %s", experience_id)
            return experience_id

        finally:
            conn.close()

    def find_similar(
        self,
        target_features: dict[str, Any],
        limit: int = 5,
    ) -> list[dict[str, Any]]:
        """Find similar experiences based on target features

        Args:
            target_features: Features to match against
            limit: Maximum number of results

        Returns:
            List of similar experiences with similarity scores

        """
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row

        try:
            # Generate embedding for query
            query_embedding = self._generate_simple_embedding(target_features, {})

            # Fetch all experiences (in a real system, we'd use vector DB)
            cursor = conn.execute(
                """
                SELECT id, title, content, target_features,
                       effectiveness_score, tags, embedding
                FROM experiences
                ORDER BY effectiveness_score DESC
                """,
            )

            results = []
            for row in cursor:
                # Calculate similarity
                stored_embedding = json.loads(row["embedding"])
                similarity = self._cosine_similarity(query_embedding, stored_embedding)

                # Parse stored data
                content = json.loads(row["content"])
                features = json.loads(row["target_features"])
                tags = json.loads(row["tags"])

                results.append(
                    {
                        "id": row["id"],
                        "title": row["title"],
                        "content": content,
                        "target_features": features,
                        "effectiveness_score": row["effectiveness_score"],
                        "tags": tags,
                        "similarity_score": similarity,
                    },
                )

            # Sort by similarity and return top N
            results.sort(key=lambda x: x["similarity_score"], reverse=True)
            return results[:limit]

        finally:
            conn.close()

    def get_by_id(self, experience_id: str) -> dict[str, Any] | None:
        """Get a specific experience by ID"""
        conn = sqlite3.connect(str(self.db_path))
        conn.row_factory = sqlite3.Row

        try:
            cursor = conn.execute(
                "SELECT * FROM experiences WHERE id = ?",
                (experience_id,),
            )
            row = cursor.fetchone()

            if row:
                return {
                    "id": row["id"],
                    "title": row["title"],
                    "content": json.loads(row["content"]),
                    "target_features": json.loads(row["target_features"]),
                    "effectiveness_score": row["effectiveness_score"],
                    "tags": json.loads(row["tags"]),
                    "created_at": row["created_at"],
                }
            return None

        finally:
            conn.close()

    def _generate_simple_embedding(
        self,
        target_features: dict[str, Any],
        content: dict[str, Any],
    ) -> list[float]:
        """Generate a simple embedding vector from features

        This is a placeholder - in production, use real embeddings
        """
        # Create a simple feature vector
        embedding = []

        # Add numeric features
        embedding.append(float(len(target_features.get("functions", []))))
        embedding.append(float(target_features.get("complexity_level", 5)) / 10.0)
        embedding.append(1.0 if target_features.get("has_loops", False) else 0.0)
        embedding.append(1.0 if target_features.get("has_arrays", False) else 0.0)

        # Add content-based features
        embedding.append(float(len(content)) / 100.0)  # Normalized content size

        # Pad to fixed size
        EMBEDDING_SIZE = 10
        while len(embedding) < EMBEDDING_SIZE:
            embedding.append(0.0)

        return embedding[:EMBEDDING_SIZE]  # Ensure fixed size

    def _cosine_similarity(self, vec1: list[float], vec2: list[float]) -> float:
        """Calculate cosine similarity between two vectors"""
        if len(vec1) != len(vec2):
            return 0.0

        dot_product = sum(a * b for a, b in zip(vec1, vec2))
        norm1 = math.sqrt(sum(a * a for a in vec1))
        norm2 = math.sqrt(sum(b * b for b in vec2))

        if norm1 * norm2 == 0:
            return 0.0

        return dot_product / (norm1 * norm2)

    def get_stats(self) -> dict[str, Any]:
        """Get database statistics"""
        conn = sqlite3.connect(str(self.db_path))
        try:
            cursor = conn.execute("SELECT COUNT(*) as count FROM experiences")
            count = cursor.fetchone()[0]

            return {
                "total_experiences": count,
                "db_path": str(self.db_path),
                "db_size_mb": (
                    self.db_path.stat().st_size / 1024 / 1024
                    if self.db_path.exists()
                    else 0
                ),
            }
        finally:
            conn.close()

    async def store_simple_experience(
        self,
        campaign_id: str,
        experience_data: dict[str, Any],
    ) -> None:
        """Store simplified experience data (async wrapper for sync method)

        Args:
            campaign_id: Campaign identifier
            experience_data: Simplified experience data dictionary

        """
        # Extract basic info for storage
        title = f"Campaign {campaign_id} - {experience_data.get('target', 'unknown')}"

        target_features = {
            "target_name": experience_data.get("target", "unknown"),
            "final_coverage": experience_data.get("metrics", {}).get("coverage", 0),
            "crashes_found": experience_data.get("metrics", {}).get(
                "unique_crashes",
                0,
            ),
        }

        # Calculate effectiveness based on coverage and crashes
        coverage = experience_data.get("metrics", {}).get("coverage", 0)
        crashes = experience_data.get("metrics", {}).get("unique_crashes", 0)
        effectiveness = min(1.0, (coverage * 0.7) + (min(crashes, 10) / 10 * 0.3))

        # Store using existing method
        self.store_experience(
            experience_id=campaign_id,
            title=title,
            content=experience_data,
            target_features=target_features,
            effectiveness_score=effectiveness,
            tags=["campaign", "reflection"],
        )

    def close(self) -> None:
        """Close the knowledge base (no-op for SQLite)"""
        # SQLite connections are closed automatically when out of scope
        logger.debug("Knowledge base closed")
