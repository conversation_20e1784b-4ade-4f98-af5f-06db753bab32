"""Simplified Prompt Management System for FuzzLM-Agent Research Prototype
========================================================================

This module provides a minimalist prompt management system to replace hardcoded prompts
throughout the project. Focused on core functionality for research prototype development.

Key Features:
- Template-based prompt generation using Jinja2
- YAML configuration support
- Basic error handling
- Simple and fast

Usage:
    from fuzzlm_agent.prompts import PromptManager

    prompt_manager = PromptManager()
    prompt = prompt_manager.get_prompt(
        'strategy.generation',
        target_path='example.c',
        language='c',
        characteristics=['memory_management']
    )
"""

from .manager import PromptManager

__all__ = ["PromptManager"]
