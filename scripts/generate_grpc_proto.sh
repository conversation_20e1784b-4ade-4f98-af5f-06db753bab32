#!/bin/bash
# gRPC Protocol Buffers代码生成脚本
# 生成gRPC控制协议的Python/Rust代码
# 包含gRPC控制协议和遥测数据protobuf

set -e

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "🔧 生成gRPC Protocol Buffers代码..."

# 检查是否安装了protoc
if ! command -v protoc &> /dev/null; then
    echo -e "${RED}错误: protoc未安装${NC}"
    echo "请安装Protocol Buffers编译器:"
    echo "  Ubuntu/Debian: sudo apt-get install protobuf-compiler"
    echo "  macOS: brew install protobuf"
    exit 1
fi

# 检查是否安装了Python protobuf包
if ! python -c "import google.protobuf" &> /dev/null; then
    echo -e "${RED}错误: Python protobuf包未安装${NC}"
    echo "请运行: pip install protobuf grpcio-tools"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 定义proto文件位置
GRPC_PROTO_DIR="$PROJECT_ROOT/fuzzlm_agent/infrastructure/grpc"
GRPC_PROTO_FILE="$GRPC_PROTO_DIR/fuzzing_control.proto"

# 生成gRPC代码（如果proto文件存在）
if [[ -f "$GRPC_PROTO_FILE" ]]; then
    echo "📝 生成Python gRPC代码..."
    cd "$GRPC_PROTO_DIR"
    python -m grpc_tools.protoc \
        --python_out=. \
        --grpc_python_out=. \
        --proto_path=. \
        fuzzing_control.proto
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✓ Python gRPC代码生成成功${NC}"
        echo "  生成文件: fuzzing_control_pb2.py, fuzzing_control_pb2_grpc.py"
        
        # 修复 gRPC 文件中的绝对导入问题
        echo "🔧 修复导入路径..."
        if [[ -f "fuzzing_control_pb2_grpc.py" ]]; then
            sed -i 's/import fuzzing_control_pb2 as/from . import fuzzing_control_pb2 as/' fuzzing_control_pb2_grpc.py
            echo -e "${GREEN}   ✅ 已修复 fuzzing_control_pb2_grpc.py 的导入路径${NC}"
        fi
    else
        echo -e "${RED}✗ Python gRPC代码生成失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}错误: 找不到fuzzing_control.proto文件${NC}"
    echo "期望位置: $GRPC_PROTO_FILE"
    exit 1
fi

# 检查LLM代码生成协议
LLM_PROTO_FILE="$GRPC_PROTO_DIR/llm_generation.proto"
if [[ -f "$LLM_PROTO_FILE" ]]; then
    echo "📝 生成LLM代码生成协议..."
    cd "$GRPC_PROTO_DIR"
    python -m grpc_tools.protoc \
        --python_out=. \
        --grpc_python_out=. \
        --proto_path=. \
        llm_generation.proto
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✓ LLM协议代码生成成功${NC}"
        echo "  生成文件: llm_generation_pb2.py, llm_generation_pb2_grpc.py"
    fi
fi

# 生成遥测数据protobuf
TELEMETRY_PROTO_DIR="$PROJECT_ROOT/fuzzlm_agent/infrastructure/shared_memory"
TELEMETRY_PROTO_FILE="$TELEMETRY_PROTO_DIR/telemetry.proto"

if [[ -f "$TELEMETRY_PROTO_FILE" ]]; then
    echo "📝 生成遥测数据protobuf..."
    cd "$TELEMETRY_PROTO_DIR"
    python -m grpc_tools.protoc \
        --python_out=. \
        --proto_path=. \
        telemetry.proto
    
    if [[ $? -eq 0 ]]; then
        echo -e "${GREEN}✓ 遥测protobuf代码生成成功${NC}"
        echo "  生成文件: telemetry_pb2.py"
        
        # 为遥测数据创建__init__.py以便导入
        if [[ ! -f "__init__.py" ]]; then
            touch "__init__.py"
            echo -e "${GREEN}   ✅ 已创建 __init__.py${NC}"
        fi
    else
        echo -e "${RED}✗ 遥测protobuf代码生成失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}警告: 找不到telemetry.proto文件${NC}"
    echo "期望位置: $TELEMETRY_PROTO_FILE"
fi

echo ""
echo -e "${GREEN}🎉 Protocol Buffers代码生成完成！${NC}"
echo ""
echo "下一步："
echo "1. Python端可以直接导入使用生成的gRPC和遥测protobuf代码"  
echo "2. Rust端通过build.rs自动编译protobuf文件"
echo "3. 遥测数据现在使用protobuf格式，提供更好的跨语言兼容性"